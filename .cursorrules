# Cursor Editor Rules for MyDeer Property Monorepo

You are an expert in TypeScript, React, Next.js, React Native, Expo, Node.js, tRPC, Prisma, and modern full-stack development.

## Project Structure
This is a Turborepo monorepo with:
- Apps: admin, customer, partner (Next.js), customer-mobile-mydeer (React Native Expo)
- Packages: database (Prisma), ui (shared components), customer-api, partner-api, admin-api (tRPC routers), customer-auth, validators, tailwind-config, tsconfig, eslint-config

## Code Style and Structure

### TypeScript
- Use TypeScript for all code; prefer `interface` over `type` for object shapes
- Enable strict mode; avoid `any`, use `unknown` for truly unknown types
- Use proper typing for tRPC procedures and Prisma models
- Leverage utility types: `Partial<T>`, `Pick<T, K>`, `Omit<T, K>`

### React/Next.js
- Use functional components with hooks; avoid class components
- Prefer composition over inheritance
- Use Server Components by default, Client Components when needed
- Implement proper error boundaries and loading states
- Use `use client` directive only when necessary

### React Native/Expo
- Use Expo managed workflow with EAS
- Implement proper safe area handling
- Use NativeWind/Tailwind for styling
- Optimize FlatList performance with proper props
- Handle offline states and network errors

### tRPC
- Define procedures with proper input/output validation using Zod
- Use middleware for authentication and logging
- Implement proper error handling with tRPC error codes
- Leverage type inference for end-to-end type safety

### Database/Prisma
- Use Prisma schema for database modeling
- Implement proper relations and constraints
- Use transactions for complex operations
- Handle database errors gracefully

## Naming Conventions
- **Variables/Functions**: camelCase (`getUserData`, `isLoading`)
- **Components**: PascalCase (`UserProfile`, `ChatScreen`)
- **Files**: kebab-case for non-components (`api-client.ts`, `user-utils.ts`)
- **Directories**: lowercase with hyphens (`user-profile`, `chat-screen`)
- **Constants**: SCREAMING_SNAKE_CASE (`API_BASE_URL`, `MAX_RETRIES`)
- **Database**: snake_case for tables/columns (`user_profiles`, `created_at`)

## Performance Best Practices
- Use React.memo() for stable components
- Implement proper memoization with useMemo/useCallback
- Optimize bundle size with dynamic imports
- Use proper caching strategies (React Query, SWR)
- Implement proper image optimization
- Use Turbo for build optimization

## Security
- Validate all inputs with Zod schemas
- Use proper authentication middleware
- Sanitize database queries
- Implement CSRF protection
- Use environment variables for secrets
- Follow OWASP security guidelines

## Error Handling
- Use proper error boundaries in React
- Implement global error handling
- Use structured logging
- Handle async errors properly
- Provide meaningful error messages

## Testing
- Write unit tests with Jest/Vitest
- Use React Testing Library for component tests
- Test user interactions, not implementation
- Mock external dependencies properly
- Implement integration tests for critical flows

## Accessibility
- Use semantic HTML elements
- Implement proper ARIA attributes
- Ensure keyboard navigation
- Test with screen readers
- Maintain proper color contrast
- Support reduced motion preferences

## File Organization
```
apps/
├── admin/                    # Admin dashboard (Next.js)
├── customer/                 # Customer portal (Next.js)
├── partner/                  # Partner portal (Next.js)
|── customer-mobile-mydeer/   # Customer Mobile app (React Native Expo)
└── partner-mobile/           # Partner mobile app (React Native Expo)

packages/
├── database/                 # Prisma schema and migrations
├── ui/                       # Shared UI components
├── customer-api/             # Customer tRPC API
├── partner-api/              # Partner tRPC API
├── admin-api/                # Admin tRPC API (if exists)
├── customer-auth/            # Customer authentication logic
├── validators/               # Shared Zod validation schemas
├── tailwind-config/          # Shared Tailwind CSS configuration
├── tsconfig/                 # TypeScript configurations
└── eslint-config/            # ESLint configurations
```

## Common Patterns

### tRPC Procedure
```typescript
export const getUserProfile = publicProcedure
  .input(z.object({ userId: z.string() }))
  .query(async ({ input, ctx }) => {
    const user = await ctx.db.user.findUnique({
      where: { id: input.userId },
      include: { profile: true }
    });
    
    if (!user) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'User not found'
      });
    }
    
    return user;
  });
```

### React Component with tRPC
```typescript
const UserProfile: React.FC<{ userId: string }> = ({ userId }) => {
  const { data: user, isLoading, error } = api.user.getUserProfile.useQuery({ userId });
  
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error.message} />;
  if (!user) return <NotFound />;
  
  return <UserProfileView user={user} />;
};
```

### Prisma Model
```prisma
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  profile   Profile?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("users")
}
```

## Environment Setup
- Use proper environment variables for different stages
- Implement proper database connection pooling
- Use Redis for caching when needed
- Set up proper logging and monitoring
- Configure proper CORS policies

## Deployment
- Use Vercel for Next.js apps
- Use EAS for React Native builds
- Implement proper CI/CD with GitHub Actions
- Use proper environment variable management
- Set up monitoring and alerting

## Key Principles
1. **Type Safety**: Leverage TypeScript and tRPC for end-to-end type safety
2. **Performance**: Optimize for Core Web Vitals and mobile performance
3. **Accessibility**: Build inclusive applications
4. **Security**: Follow security best practices
5. **Maintainability**: Write clean, testable, and documented code
6. **User Experience**: Prioritize user experience and error handling
7. **Scalability**: Design for growth and maintainability

Always consider the monorepo structure when making changes and ensure consistency across all applications and packages.