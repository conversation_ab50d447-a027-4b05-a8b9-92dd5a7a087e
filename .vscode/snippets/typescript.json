{"tRPC Procedure": {"prefix": "trpc-procedure", "body": ["export const ${1:procedureName} = ${2:publicProcedure}", "  .input(z.object({", "    ${3:id}: z.string()", "  }))", "  .${4:query}(async ({ input, ctx }) => {", "    ${5:// Implementation}", "    return ${6:result};", "  });"], "description": "Create a tRPC procedure"}, "React Component": {"prefix": "rfc", "body": ["interface ${1:ComponentName}Props {", "  ${2:prop}: ${3:string};", "}", "", "const ${1:ComponentName}: React.FC<${1:ComponentName}Props> = ({ ${2:prop} }) => {", "  return (", "    <div>", "      ${4:// Component content}", "    </div>", "  );", "};", "", "export default ${1:ComponentName};"], "description": "React Functional Component with TypeScript"}, "Custom Hook": {"prefix": "use-hook", "body": ["import { useState, useEffect } from 'react';", "", "interface Use${1:HookName}Return {", "  ${2:data}: ${3:any};", "  ${4:isLoading}: boolean;", "  ${5:error}: Error | null;", "}", "", "export function use${1:HookName}(${6:params}): Use${1:HookName}Return {", "  const [${2:data}, set${2/(.*)/${1:/capitalize}/}] = useState<${3:any}>(null);", "  const [${4:isLoading}, set${4/(.*)/${1:/capitalize}/}] = useState(false);", "  const [${5:error}, set${5/(.*)/${1:/capitalize}/}] = useState<Error | null>(null);", "", "  useEffect(() => {", "    ${7:// Hook logic}", "  }, [${8:dependencies}]);", "", "  return { ${2:data}, ${4:isLoading}, ${5:error} };", "}"], "description": "Custom React Hook with TypeScript"}, "Prisma Model": {"prefix": "prisma-model", "body": ["model ${1:ModelName} {", "  id        String   @id @default(cuid())", "  ${2:field}    ${3:String}", "  createdAt DateTime @default(now()) @map(\"created_at\")", "  updatedAt DateTime @updatedAt @map(\"updated_at\")", "", "  @@map(\"${4:table_name}\")", "}"], "description": "Prisma Model Definition"}, "API Route Handler": {"prefix": "api-route", "body": ["import { NextRequest, NextResponse } from 'next/server';", "", "export async function ${1:GET}(request: NextRequest) {", "  try {", "    ${2:// Implementation}", "    ", "    return NextResponse.json({ ${3:data} });", "  } catch (error) {", "    console.error('${4:Error message}:', error);", "    return NextResponse.json(", "      { error: '${5:Internal server error}' },", "      { status: 500 }", "    );", "  }", "}"], "description": "Next.js API Route Handler"}, "React Native Component": {"prefix": "rnfc", "body": ["import React from 'react';", "import { View, Text, StyleSheet } from 'react-native';", "", "interface ${1:ComponentName}Props {", "  ${2:prop}: ${3:string};", "}", "", "const ${1:ComponentName}: React.FC<${1:ComponentName}Props> = ({ ${2:prop} }) => {", "  return (", "    <View style={styles.container}>", "      <Text>${4:Component content}</Text>", "    </View>", "  );", "};", "", "const styles = StyleSheet.create({", "  container: {", "    flex: 1,", "  },", "});", "", "export default ${1:ComponentName};"], "description": "React Native Functional Component"}, "Zod Schema": {"prefix": "zod-schema", "body": ["const ${1:schemaName}Schema = z.object({", "  ${2:field}: z.${3:string}()${4:.min(1, '${5:Field is required}')},", "});", "", "type ${1/(.*)/${1:/capitalize}/} = z.infer<typeof ${1:schemaName}Schema>;"], "description": "<PERSON>od Sc<PERSON> with TypeScript inference"}}