{"version": "0.2.0", "configurations": [{"name": "Debug Admin App", "type": "node", "request": "launch", "program": "${workspaceFolder}/apps/admin/node_modules/.bin/next", "args": ["dev", "-p", "3001"], "cwd": "${workspaceFolder}/apps/admin", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "serverReadyAction": {"pattern": "ready - started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}, {"name": "Debug Customer App", "type": "node", "request": "launch", "program": "${workspaceFolder}/apps/customer/node_modules/.bin/next", "args": ["dev", "-p", "3000"], "cwd": "${workspaceFolder}/apps/customer", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "serverReadyAction": {"pattern": "ready - started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}, {"name": "Debug Partner App", "type": "node", "request": "launch", "program": "${workspaceFolder}/apps/partner/node_modules/.bin/next", "args": ["dev", "-p", "3002"], "cwd": "${workspaceFolder}/apps/partner", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "serverReadyAction": {"pattern": "ready - started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}, {"name": "Debug React Native (Expo)", "type": "reactnative", "request": "launch", "platform": "ios", "cwd": "${workspaceFolder}/apps/customer-mobile"}, {"name": "Debug React Native (Android)", "type": "reactnative", "request": "launch", "platform": "android", "cwd": "${workspaceFolder}/apps/customer-mobile"}], "compounds": [{"name": "Debug All Apps", "configurations": ["Debug Admin App", "Debug Customer App", "Debug Partner App"]}]}