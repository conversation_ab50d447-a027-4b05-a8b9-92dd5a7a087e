{"version": "2.0.0", "tasks": [{"label": "Build All", "type": "shell", "command": "turbo", "args": ["build"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "Dev All", "type": "shell", "command": "turbo", "args": ["dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "isBackground": true, "problemMatcher": {"owner": "typescript", "source": "ts", "applyTo": "closedDocuments", "fileLocation": ["relative", "${cwd}"], "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": {"regexp": "(.*?)"}, "endsPattern": {"regexp": "ready - started server on"}}}}, {"label": "Lint All", "type": "shell", "command": "turbo", "args": ["lint"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$eslint-stylish"]}, {"label": "Type Check All", "type": "shell", "command": "turbo", "args": ["typecheck"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "Clean All", "type": "shell", "command": "turbo", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Database Migration Dev", "type": "shell", "command": "yarn", "args": ["database:migration:dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Database Migration Prod", "type": "shell", "command": "yarn", "args": ["database:migration:prod"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Format All", "type": "shell", "command": "yarn", "args": ["format"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}