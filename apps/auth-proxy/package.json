{"name": "@repo/auth-proxy", "private": true, "version": "0.1.0", "type": "module", "scripts": {"build": "nitro build", "clean": "git clean -xdf .cache .nitro .output .turbo .vercel node_modules", "lint": "eslint", "format": "prettier --check . --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/core": "0.34.2", "@repo/database": "*", "@repo/msg91": "*"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/prettier-config": "*", "@repo/tailwind-config": "*", "@repo/tsconfig": "*", "@types/node": "20.19.12", "eslint": "^9.12.0", "h3": "^1.13.0", "nitropack": "^2.9.7", "prettier": "^3.3.3", "typescript": "^5.8.2"}, "prettier": "@repo/prettier-config", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}