{"name": "partner-mobile", "version": "0.0.8", "private": true, "main": "./entry.js", "scripts": {"start": "expo start --dev-client", "prebuild": "cross-env EXPO_NO_DOTENV=1 yarn expo prebuild", "android": "cross-env EXPO_NO_METRO_WORKSPACE_ROOT=1 EXPO_NO_DOTENV=1 expo run:android", "ios": "cross-env EXPO_NO_METRO_WORKSPACE_ROOT=1 EXPO_NO_DOTENV=1 expo run:ios", "xcode": "xed -b ios", "doctor": "npx expo-doctor@latest", "start:staging": "cross-env APP_ENV=staging yarn run start", "prebuild:staging": "cross-env APP_ENV=staging yarn run prebuild", "android:staging": "cross-env APP_ENV=staging yarn run android", "ios:staging": "cross-env APP_ENV=staging yarn run ios", "start:production": "cross-env APP_ENV=production yarn run start", "prebuild:production": "cross-env APP_ENV=production yarn run prebuild", "android:production": "cross-env APP_ENV=production yarn run android", "ios:production": "cross-env APP_ENV=production yarn run ios", "build:development:ios": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform ios", "build:development:android": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform android ", "build:staging:ios": "cross-env APP_ENV=staging EXPO_NO_DOTENV=1 eas build --profile staging --platform ios", "build:staging:android": "cross-env APP_ENV=staging EXPO_NO_DOTENV=1 eas build --profile staging --platform android ", "build:production:ios": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform ios", "build:production:android": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform android ", "app-release": "cross-env SKIP_BRANCH_PROTECTION=true np --no-publish --no-cleanup --no-release-draft", "version": "yarn run prebuild && git add .", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc  --noemit", "lint:translations": "eslint ./src/translations/ --fix --ext .json  ", "test": "jest", "check-all": "yarn run lint && yarn run type-check && yarn run lint:translations && yarn run test", "test:ci": "yarn run test --coverage", "test:watch": "yarn run test --watch", "install-maestro": "curl -Ls 'https://get.maestro.mobile.dev' | bash", "e2e-test": "maestro test .maestro/ -e APP_ID=com.obytes.development"}, "dependencies": {"@azure/core-asynciterator-polyfill": "^1.0.2", "@expo/metro-runtime": "~6.1.1", "@expo/react-native-action-sheet": "^4.1.1", "@gorhom/bottom-sheet": "5.2.6", "@hookform/resolvers": "^3.9.1", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/slider": "5.0.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/material-top-tabs": "^6.6.14", "@react-navigation/native": "^7.0.14", "@sentry/react-native": "~6.20.0", "@shopify/flash-list": "2.0.2", "@tanstack/react-query": "^5", "@tanstack/react-query-devtools": "^5", "@trpc/client": "^11.2.0", "@trpc/react-query": "^11.2.0", "@trpc/server": "^11.2.0", "@trpc/tanstack-react-query": "^11.2.0", "app-icon-badge": "^0.1.2", "axios": "^1.7.9", "date-fns": "^4.1.0", "expo": "^54.0.0-preview.12", "expo-blur": "~15.0.5", "expo-build-properties": "~1.0.6", "expo-checkbox": "~5.0.5", "expo-clipboard": "~8.0.5", "expo-constants": "~18.0.6", "expo-dev-client": "~6.0.7", "expo-document-picker": "~14.0.5", "expo-font": "~14.0.6", "expo-google-places-autocomplete": "^1.2.0", "expo-image": "~3.0.5", "expo-image-picker": "~17.0.5", "expo-linear-gradient": "~15.0.5", "expo-linking": "~8.0.6", "expo-localization": "~17.0.5", "expo-location": "~19.0.5", "expo-router": "~6.0.0-preview.12", "expo-secure-store": "~15.0.5", "expo-sharing": "~14.0.5", "expo-splash-screen": "~31.0.7", "expo-status-bar": "~3.0.6", "expo-system-ui": "~6.0.5", "expo-updates": "~29.0.7", "expo-video": "~3.0.7", "expo-web-browser": "~15.0.5", "i18next": "^23.16.8", "lodash.memoize": "^4.1.2", "meilisearch": "^0.49.0", "nativewind": "~4.1.23", "onesignal-expo-plugin": "^2.0.3", "react": "19.1.0", "react-error-boundary": "^4.1.2", "react-google-autocomplete": "2.7.3", "react-hook-form": "^7.54.0", "react-i18next": "^15.1.4", "react-native": "0.81.1", "react-native-edge-to-edge": "1.6.0", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "~2.28.0", "react-native-get-random-values": "^1.11.0", "react-native-gifted-charts": "^1.4.48", "react-native-google-places-textinput": "^0.7.4", "react-native-keyboard-controller": "1.18.5", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "1.26.1", "react-native-mmkv": "~3.3.1", "react-native-onesignal": "^5.2.12", "react-native-otp-entry": "^1.8.1", "react-native-pager-view": "6.9.1", "react-native-reanimated": "~4.0.2", "react-native-restart": "0.0.27", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.15.4", "react-native-svg": "15.12.1", "react-native-svg-transformer": "^1.5.1", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.2.1", "react-native-web": "^0.21.0", "react-native-worklets": "0.4.0", "react-query-kit": "^3.3.1", "rn-eventsource-reborn": "^1.0.5", "string-width": "^7.2.0", "strip-ansi": "^7.1.0", "superjson": "2.2.1", "tailwind-variants": "^1.0.0", "web-streams-polyfill": "^4.0.0", "wrap-ansi": "^9.0.0", "zod": "^3.24.1", "zustand": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/runtime": "^7.26.10", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@dev-plugins/react-query": "^0.3.1", "@expo/config": "~12.0.6", "@repo/partner-api": "*", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react-native": "^12.9.0", "@types/babel__core": "^7.20.5", "@types/i18n-js": "^3.8.9", "@types/jest": "^29.5.14", "@types/lodash.memoize": "^4.1.9", "@types/react": "19.1.12", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "eslint": "^9.12.0", "eslint-config-expo": "~10.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-i18n-json": "^4.0.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-testing-library": "^6.5.0", "eslint-plugin-unicorn": "^46.0.1", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-expo": "~54.0.6", "jest-junit": "^16.0.0", "lint-staged": "^15.2.11", "np": "^10.1.0", "prettier": "^3.3.3", "tailwindcss": "^3.4.15", "ts-jest": "^29.2.5", "typescript": "^5.8.2"}, "repository": {"type": "git", "url": "git+https://github.com/user/repo-name.git"}, "osMetadata": {"initVersion": "6.3.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}