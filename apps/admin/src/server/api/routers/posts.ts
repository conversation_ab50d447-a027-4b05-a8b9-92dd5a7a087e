import { TRPCError } from "@trpc/server";
import { adminProcedure, createTRPCRouter, protectedProcedure } from "../trpc";
import * as Sentry from "@sentry/nextjs";
import { z } from "zod";
import whereAdminConditionCreatedByAdminId from "../utils/where-admin-condition-created-by-admin-id";
import { logPostAction, extractAuditContext } from "@repo/database/src/audit-logger";

const postsRouter = createTRPCRouter({
  getPostsByUserId: protectedProcedure
    .input(z.object({ userId: z.string() }))
    .query(({ ctx, input }) => {
      // Build where condition based on user role
      const whereAdminCondition = whereAdminConditionCreatedByAdminId(ctx.session.user.role, ctx.session.user.id);

      const { userId } = input;

      return ctx.db.post.findMany({
        where: {
          user: {
            ...whereAdminCondition,
          },
          userId: userId,
          deletedAt: null,
        },
        orderBy: {
          createdAt: "desc",
        },
        select: {
          id: true,
          content: true,
          totalComments: true,
          totalLikes: true,
          createdAt: true,
          updatedAt: true,

          user: {
            select: {
              id: true,
              filePublicUrl: true,
              cloudinaryProfileImageUrl: true,
              name: true,
              company: {
                select: {
                  companyName: true,
                },
              },
            },
          },
          media: {
            select: {
              filePublicUrl: true,
              mediaType: true,
              cloudinaryId: true,
              cloudinaryUrl: true,
            },
          },
          comments: {
            select: {
              comment: true,
              isPinned: true,
              createdAt: true,
              userId: true,
              customerId: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  filePublicUrl: true,
                  companyDetails: {
                    select: {
                      companyName: true,
                    },
                  },
                },
              },
              customer: {
                select: {
                  id: true,
                  name: true,
                  profileImagePublicUrl: true,
                },
              },
            },
          },
          likes: {
            select: {
              id: true,
              postId: true,
            },
            take: 1,
          },
        },
      });
    }),

  //   getAllReportedPosts: protectedProcedure.query(({ ctx }) => {
  //     const userId = ctx.session.user.id;
  //     return ctx.db.post.findMany({
  //       where: {
  //         reportCount: {
  //           gt: 0,
  //         },
  //       },
  //       select: {
  //         id: true,
  //         content: true,
  //         totalComments: true,
  //         totalLikes: true,
  //         createdAt: true,
  //         updatedAt: true,

  //         user: {
  //           select: {
  //             id: true,
  //             filePublicUrl: true,
  //             name: true,
  //             company: {
  //               select: {
  //                 companyName: true,
  //               },
  //             },
  //           },
  //         },
  //         media: {
  //           select: {
  //             filePublicUrl: true,
  //             mediaType: true,
  //           },
  //         },
  //         comments: {
  //           select: {
  //             comment: true,
  //             isPinned: true,
  //             createdAt: true,
  //             user: {
  //               select: {
  //                 id: true,
  //                 name: true,
  //                 filePublicUrl: true,
  //                 companyDetails: {
  //                   select: {
  //                     companyName: true,
  //                   },
  //                 },
  //               },
  //             },
  //           },
  //         },
  //         likes: {
  //           where: {
  //             userId: userId,
  //           },
  //           select: {
  //             id: true,
  //             postId: true,
  //           },
  //           take: 1,
  //         },
  //       },
  //       orderBy: {
  //         createdAt: "desc",
  //       },
  //     });
  //   }),
  // On Admin are allowed to see all reported posts and perform actions on them
  getAllReportedPosts: adminProcedure.query(({ ctx }) => {
    const userId = ctx.session.user.id;
    return ctx.db.post.findMany({
      where: {
        // deletedAt: null,
        reportedPost: {
          some: {},
        },
      },
      select: {
        id: true,
        content: true,
        totalComments: true,
        totalLikes: true,
        reportCount: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
        media: {
          select: {
            filePublicUrl: true,
            mediaType: true,
            cloudinaryId: true,
            cloudinaryUrl: true,
          },
        },
        user: {
          select: {
            id: true,
            filePublicUrl: true,
            cloudinaryProfileImageUrl: true,
            name: true,
            company: {
              select: {
                companyName: true,
              },
            },
          },
        },
        comments: {
          select: {
            comment: true,
            isPinned: true,
            createdAt: true,
            userId: true,
            customerId: true,
            user: {
              select: {
                id: true,
                name: true,
                filePublicUrl: true,
                companyDetails: {
                  select: {
                    companyName: true,
                  },
                },
              },
            },
            customer: {
              select: {
                id: true,
                name: true,
                profileImagePublicUrl: true,
              },
            },
          },
        },
        likes: {
          where: {
            userId: userId,
          },
          select: {
            id: true,
            postId: true,
          },
          take: 1,
        },
        reportedPost: {
          select: {
            reason: true,
            createdAt: true,
            updatedAt: true,
            user: {
              select: {
                id: true,
                filePublicUrl: true,
                cloudinaryProfileImageUrl: true,
                name: true,
                company: {
                  select: {
                    companyName: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }),

  deletePost: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get post details before deletion for audit log
        const postToDelete = await ctx.db.post.findUnique({
          where: { id: input.id },
          select: {
            id: true,
            content: true,
            userId: true,
            deletedAt: true,
            user: {
              select: {
                name: true,
              },
            },
          },
        });

        if (!postToDelete) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Post not found",
          });
        }

        const deletePost = await ctx.db.post.update({
          where: {
            id: input.id,
          },
          data: {
            deletedAt: new Date(),
          },
        });

        // Log post deletion
        const auditContext = extractAuditContext(ctx);
        await logPostAction(
          ctx.db,
          "DELETE",
          input.id,
          postToDelete.content,
          auditContext,
          {
            oldValues: { deletedAt: postToDelete.deletedAt },
            newValues: { deletedAt: deletePost.deletedAt },
            metadata: {
              userId: postToDelete.userId,
              userName: postToDelete.user.name,
              notificationSent: true,
            }
          }
        );

        await ctx.db.notification.create({
          data: {
            receiverId: deletePost.userId,
            description: `Your post with content - "${deletePost.content.length > 12 ? deletePost.content.slice(0, 12) + "..." : deletePost.content}" has been deleted by the admin `,
            title: "Post Update",
            metaData: { active: false, postId: deletePost.id },
            type: "POST",
          },
        });
        return {
          message: `Post deleted successfully.`,
        };
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        Sentry.captureException(error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete post",
        });
      }
    }),

  activatePost: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Get post details before activation for audit log
        const postToActivate = await ctx.db.post.findUnique({
          where: { id: input.id },
          select: {
            id: true,
            content: true,
            userId: true,
            deletedAt: true,
            user: {
              select: {
                name: true,
              },
            },
          },
        });

        if (!postToActivate) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Post not found",
          });
        }

        const activePost = await ctx.db.post.update({
          where: {
            id: input.id,
          },
          data: {
            deletedAt: null,
          },
        });

        // Log post activation
        const auditContext = extractAuditContext(ctx);
        await logPostAction(
          ctx.db,
          "ACTIVATE",
          input.id,
          postToActivate.content,
          auditContext,
          {
            oldValues: { deletedAt: postToActivate.deletedAt },
            newValues: { deletedAt: null },
            metadata: {
              userId: postToActivate.userId,
              userName: postToActivate.user.name,
              notificationSent: true,
            }
          }
        );

        await ctx.db.notification.create({
          data: {
            receiverId: activePost.userId,
            description: `Your post with content - "${activePost.content.length > 12 ? activePost.content.slice(0, 12) + "..." : activePost.content}" has been activated by the admin `,
            title: "Post Update",
            metaData: { active: true, postId: activePost.id },
            type: "POST",
          },
        });
        return {
          message: `Post activated successfully.`,
        };
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        Sentry.captureException(error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to activate post",
        });
      }
    }),
});

export default postsRouter;
