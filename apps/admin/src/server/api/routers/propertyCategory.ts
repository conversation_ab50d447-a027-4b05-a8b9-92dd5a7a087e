import { TRPCError } from "@trpc/server";
import { createTRPCRouter, adminProcedure, protectedProcedure } from "../trpc";
import { z } from "zod";
import addUpdateCategoriesSchema from "../validations/add-update-categories.validation";
import { PostPropertyFormFieldStatusEnum, Prisma } from "@repo/database";

const propertyCategoryRouter = createTRPCRouter({
  // Need to have all logged in users access to get categories
  getAllCategories: protectedProcedure.query(async ({ ctx }) => {
    try {
      const allCategories = await ctx.db.propertyCategories.findMany({
        orderBy: {
          createdAt: "desc",
        },
      });
      return allCategories;
    } catch (error) {
      console.error("Error fetching categories:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch categories. Please try again later.",
        cause: error,
      });
    }
  }),

  // Need to have all logged in users access to get categories
  getCategoryById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { id } = input;
      try {
        const category = await ctx.db.propertyCategories.findUnique({
          where: {
            id,
          },
        });

        if (!category) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `Category with ID ${id} not found.`,
          });
        }

        return category;
      } catch (error) {
        // Check if this is already a TRPC error
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error(`Error fetching category ${id}:`, error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch category details. Please try again later.",
          cause: error,
        });
      }
    }),

  addCategory: adminProcedure
    .input(
      addUpdateCategoriesSchema.extend({
        showBedrooms: z.nativeEnum(PostPropertyFormFieldStatusEnum, {
          message: "Required",
          required_error: "Please select a value",
          invalid_type_error: "Please select a value",
        }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const {
        name,
        showBedrooms,
        showAboutProperty,
        showAmenities,
        showArea,
        showAreaIn,
        showBathrooms,
        showBuildYear,
        showCarParking,
        showFacing,
        showFloorNumber,
        showFurnishing,
        showPossessionState,
        showPropertyAddress,
        showPropertyLocation,
        showPropertyState,
        showSecurityDeposit,
        showSocietyName,
        showTotalFloors,
        showUtilities,
      } = input;

      try {
        // Check if a category with the same name already exists
        const isExisting = await ctx.db.propertyCategories.findFirst({
          where: {
            name: { contains: name, mode: "insensitive" },
          },
        });

        if (isExisting) {
          return {
            message: `Category "${isExisting.name}" already exists.`,
            warning: true,
          };
        }

        const newCategory = await ctx.db.propertyCategories.create({
          data: {
            name,
            showBedrooms,
            showAboutProperty,
            showAmenities,
            showArea,
            showAreaIn,
            showBathrooms,
            showBuildYear,
            showCarParking,
            showFacing,
            showFloorNumber,
            showFurnishing,
            showPossessionState,
            showPropertyAddress,
            showPropertyLocation,
            showPropertyState,
            showSecurityDeposit,
            showSocietyName,
            showTotalFloors,
            showUtilities,
          },
        });

        return {
          message: `Category "${newCategory.name}" created successfully.`,
          success: true,
          data: newCategory,
        };
      } catch (error) {
        console.error("Error adding category:", error);

        // Determine specific error types
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2002") {
            throw new TRPCError({
              code: "CONFLICT",
              message: "A category with this name already exists.",
              cause: error,
            });
          }
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to add new category. Please try again later.",
          cause: error,
        });
      }
    }),

  updateCategory: adminProcedure
    .input(
      addUpdateCategoriesSchema.extend({
        id: z.string(),
        showBedrooms: z.nativeEnum(PostPropertyFormFieldStatusEnum, {
          message: "Required",
          required_error: "Please select a value",
          invalid_type_error: "Please select a value",
        }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const {
        name,
        id,
        showBedrooms,
        showAboutProperty,
        showAmenities,
        showArea,
        showAreaIn,
        showBathrooms,
        showBuildYear,
        showCarParking,
        showFacing,
        showFloorNumber,
        showFurnishing,
        showPossessionState,
        showPropertyAddress,
        showPropertyLocation,
        showPropertyState,
        showSecurityDeposit,
        showSocietyName,
        showTotalFloors,
        showUtilities,
      } = input;

      try {
        // First check if the category exists
        const existingCategory = await ctx.db.propertyCategories.findUnique({
          where: { id },
        });

        if (!existingCategory) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `Category with ID ${id} not found.`,
          });
        }

        // Check if another category with the same name exists (excluding current)
        const duplicateName = await ctx.db.propertyCategories.findFirst({
          where: {
            name: { contains: name, mode: "insensitive" },
            id: { not: id },
          },
        });

        if (duplicateName) {
          return {
            message: `Another category named "${duplicateName.name}" already exists.`,
            warning: true,
          };
        }

        const updatedCategory = await ctx.db.propertyCategories.update({
          where: {
            id,
          },
          data: {
            name,
            showBedrooms,
            showAboutProperty,
            showAmenities,
            showArea,
            showAreaIn,
            showBathrooms,
            showBuildYear,
            showCarParking,
            showFacing,
            showFloorNumber,
            showFurnishing,
            showPossessionState,
            showPropertyAddress,
            showPropertyLocation,
            showPropertyState,
            showSecurityDeposit,
            showSocietyName,
            showTotalFloors,
            showUtilities,
          },
        });

        return {
          message: `"${updatedCategory.name}" updated successfully!`,
          success: true,
          data: updatedCategory,
        };
      } catch (error) {
        console.error("Error updating category:", error);

        // Check if this is already a TRPC error (e.g., NOT_FOUND)
        if (error instanceof TRPCError) {
          throw error;
        }

        // Handle specific Prisma errors
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2002") {
            throw new TRPCError({
              code: "CONFLICT",
              message: "A category with this name already exists.",
              cause: error,
            });
          }
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update category. Please try again later.",
          cause: error,
        });
      }
    }),

  removeCategory: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;

      try {
        // First check if the category exists
        const existingCategory = await ctx.db.propertyCategories.findUnique({
          where: { id },
        });

        if (!existingCategory) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `Category with ID ${id} not found.`,
          });
        }

        const category = await ctx.db.propertyCategories.delete({
          where: { id },
        });

        return {
          message: `"${category.name}" deleted successfully!`,
          success: true,
        };
      } catch (error) {
        console.error("Error deleting category:", error);

        // Check if this is already a TRPC error (e.g., NOT_FOUND)
        if (error instanceof TRPCError) {
          throw error;
        }

        // Handle specific Prisma errors
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          // Foreign key constraint error
          if (error.code === "P2003") {
            throw new TRPCError({
              code: "PRECONDITION_FAILED",
              message:
                "Cannot delete category as it is referenced by other records.",
              cause: error,
            });
          }
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete category. Please try again later.",
          cause: error,
        });
      }
    }),
});

export default propertyCategoryRouter;
