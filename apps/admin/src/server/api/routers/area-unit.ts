import { z } from "zod";
import { createTRPCRouter, adminProcedure, protectedProcedure } from "../trpc";
import { TRPCError } from "@trpc/server";
import AddUpdateAreaUnitSchema from "../validations/add-update-area-units.validation";
import * as Sentry from "@sentry/nextjs";

const areaUnitRouter = createTRPCRouter({
  // Need to have all logged in users access to get ammenties
  getAllAreaUnits: protectedProcedure.query(({ ctx }) => {
    return ctx.db.areaUnit.findMany({
      include: {
        category: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }),

  getAreaUnitById: adminProcedure
    .input(z.object({ areaUnitId: z.string() }))
    .query(({ ctx, input }) => {
      return ctx.db.areaUnit.findUnique({
        where: {
          id: input.areaUnitId,
        },
        include: {
          category: true,
        },
      });
    }),

  addAreaUnit: adminProcedure
    .input(AddUpdateAreaUnitSchema)
    .mutation(async ({ ctx, input }) => {
      const { category, name, shortForm, conversionMultiplyer } = input;

      try {
        const newAreaUnit = await ctx.db.areaUnit.create({
          data: {
            name: name,
            shortForm: shortForm,
            conversionMultiplyer: conversionMultiplyer,
            category: {
              connect: category.map((a) => ({
                id: a.id,
              })),
            },
          },
          include: {
            category: true,
          },
        });

        return {
          message: `${newAreaUnit.name} area unit created successfully`,
        };
      } catch (error) {
        Sentry.captureException(error);
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to create new area unit!",
        });
      }
    }),

  updateAreaUnit: adminProcedure
    .input(AddUpdateAreaUnitSchema.extend({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { category, id, name, shortForm, conversionMultiplyer } = input;

      try {
        const query = await ctx.db.areaUnit.update({
          where: {
            id: id,
          },
          data: {
            name: name,
            shortForm: shortForm,
            conversionMultiplyer: conversionMultiplyer,
            category: {
              set: category.map((a) => ({
                id: a.id,
              })),
            },
          },
          include: {
            category: true,
          },
        });
        return { message: `${query.name} updated successfully!` };
      } catch (error) {
        Sentry.captureException(error);
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to update area unit!",
        });
      }
    }),

  deleteAreaUnit: adminProcedure
    .input(z.object({ areaUnitId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const id = input.areaUnitId;
      try {
        const query = await ctx.db.areaUnit.delete({
          where: {
            id: id,
          },
        });
        return { message: `Area Unit ${query.name} deleted successfully` };
      } catch (error) {
        Sentry.captureException(error);
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to delete area unit",
        });
      }
    }),
});

export default areaUnitRouter;
