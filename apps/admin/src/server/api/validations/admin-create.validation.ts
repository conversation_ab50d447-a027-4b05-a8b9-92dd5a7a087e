import { z } from "zod";
import { AdminUserTypeEnum } from "@repo/database";

const AddUpdateAdminSchema = z.object({
  name: z.string().min(2, { message: "Name must of 2 chars" }),
  email: z.string().email(),
  active: z.boolean().optional(),
  userType: z.nativeEnum(AdminUserTypeEnum, {
    required_error: "Please select a value",
    invalid_type_error: "Please select a value",
  }),
  password: z.union([
    z.string().min(8, { message: "Password must be of 8 digits" }),
    z.literal(""),
  ]).optional(),
});

export default AddUpdateAdminSchema;
