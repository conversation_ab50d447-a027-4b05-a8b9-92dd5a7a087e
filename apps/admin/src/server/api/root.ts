import { createCallerFactory, createTR<PERSON>Router } from "~/server/api/trpc";
import partnerRouter from "./routers/partner";
import adminRouter from "./routers/admin";
import propertyRouter from "./routers/property";
import amenitiesRouter from "./routers/amenities";
import { awsRouter } from "./routers/aws";
import enquiryRouter from "./routers/enquiry";
import faqRouter from "./routers/faq";
import newsRouter from "./routers/news";
import cityRouter from "./routers/city";
import propertyCategoryRouter from "./routers/propertyCategory";
import postsRouter from "./routers/posts";
import areaUnitRouter from "./routers/area-unit";
import careerRouter from "./routers/career";
import jobRoleRouter from "./routers/jobRole";
import deleteAccountRouter from "./routers/delete-account";
import { languageRouter } from "./routers/language";
import { agentReviewsRouter } from "./routers/agent-reviews";
import { customerTestimonialsRouter } from "./routers/customer-testimonials";
import reportedUsersFromChatRouter from "./routers/reported-users-from-chat";
import customerRouter from "./routers/customers";
import auditLogsRouter from "./routers/audit-logs";
/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  aws: awsRouter,
  partner: partnerRouter,
  admin: adminRouter,
  property: propertyRouter,
  amenities: amenitiesRouter,
  propertyCategories: propertyCategoryRouter,
  enquiries: enquiryRouter,
  faq: faqRouter,
  news: newsRouter,
  city: cityRouter,
  post: postsRouter,
  areaUnit: areaUnitRouter,
  career: careerRouter,
  jobRole: jobRoleRouter,
  deleteAccounts: deleteAccountRouter,
  language: languageRouter,
  agentReviews: agentReviewsRouter,
  customerTestimonails: customerTestimonialsRouter,
  reportedUsersFromChat: reportedUsersFromChatRouter,
  customer: customerRouter,
  auditLogs: auditLogsRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
