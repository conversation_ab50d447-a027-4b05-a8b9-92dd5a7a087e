"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@repo/ui/components/ui/card";
import { Badge } from "@repo/ui/components/ui/badge";
import { Activity, Users, FileText, TrendingUp } from "lucide-react";

interface StatsData {
  totalLogs: number;
  recentLogs: number;
  actionStats: {
    action: string;
    count: number;
  }[];
  entityStats: {
    entityType: string;
    count: number;
  }[];
  adminStats: {
    adminId: string;
    adminName: string;
    count: number;
  }[];
}

interface AuditLogStatsProps {
  stats: StatsData;
}

export function AuditLogStats({ stats }: AuditLogStatsProps) {
  const getActionColor = (action: string) => {
    switch (action) {
      case "CREATE":
        return "bg-green-100 text-green-800";
      case "UPDATE":
        return "bg-blue-100 text-blue-800";
      case "DELETE":
        return "bg-red-100 text-red-800";
      case "APPROVE":
      case "ACTIVATE":
        return "bg-emerald-100 text-emerald-800";
      case "REJECT":
      case "DEACTIVATE":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getEntityColor = (entityType: string) => {
    switch (entityType) {
      case "PROPERTY":
        return "bg-blue-100 text-blue-800";
      case "PARTNER":
        return "bg-green-100 text-green-800";
      case "POST":
        return "bg-purple-100 text-purple-800";
      case "USER":
        return "bg-orange-100 text-orange-800";
      case "COMPANY":
        return "bg-cyan-100 text-cyan-800";
      case "ADMIN_USER":
        return "bg-pink-100 text-pink-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Logs */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Audit Logs</CardTitle>
          <FileText className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalLogs.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">All time</p>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.recentLogs.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">Last 30 days</p>
        </CardContent>
      </Card>

      {/* Top Actions */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Top Actions</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {stats.actionStats.slice(0, 3).map((action) => (
              <div key={action.action} className="flex items-center justify-between">
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getActionColor(action.action)}`}
                >
                  {action.action}
                </Badge>
                <span className="text-sm font-medium">{action.count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Active Admins */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Admins</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {stats.adminStats.slice(0, 3).map((admin) => (
              <div key={admin.adminId} className="flex items-center justify-between">
                <div className="text-sm font-medium truncate">
                  {admin.adminName}
                </div>
                <Badge variant="secondary" className="text-xs">
                  {admin.count}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Entity Breakdown */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">Entity Activity (Last 30 Days)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {stats.entityStats.map((entity) => (
              <div key={entity.entityType} className="flex items-center justify-between">
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getEntityColor(entity.entityType)}`}
                >
                  {entity.entityType}
                </Badge>
                <span className="text-sm font-medium">{entity.count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Action Breakdown */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">Action Breakdown (Last 30 Days)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {stats.actionStats.map((action) => (
              <div key={action.action} className="flex items-center justify-between">
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getActionColor(action.action)}`}
                >
                  {action.action}
                </Badge>
                <span className="text-sm font-medium">{action.count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
