"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@repo/ui/components/ui/dialog";
import { Badge } from "@repo/ui/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@repo/ui/components/ui/card";
import { Separator } from "@repo/ui/components/ui/separator";
import { format } from "date-fns";
import { api } from "~/trpc/react";
import { Loader2, User, Calendar, FileText, Settings, Info } from "lucide-react";

interface AuditLogDetailsDialogProps {
  logId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AuditLogDetailsDialog({ logId, open, onOpenChange }: AuditLogDetailsDialogProps) {
  const { data: auditLog, isLoading } = api.auditLogs.getAuditLogDetails.useQuery(
    { id: logId },
    { enabled: open && !!logId }
  );

  const formatJsonValue = (value: unknown): string => {
    if (value === null || value === undefined) {
      return "null";
    }
    if (typeof value === "string") {
      return value;
    }
    if (typeof value === "boolean") {
      return value.toString();
    }
    if (typeof value === "number") {
      return value.toString();
    }
    if (value instanceof Date) {
      return format(value, "PPP pp");
    }
    return JSON.stringify(value, null, 2);
  };

  const renderChanges = (changes: unknown) => {
    if (!changes || typeof changes !== 'object') {
      return <div className="text-sm text-muted-foreground">No changes recorded</div>;
    }

    const changeEntries = Object.entries(changes);
    if (changeEntries.length === 0) {
      return <div className="text-sm text-muted-foreground">No changes recorded</div>;
    }

    return (
      <div className="space-y-3">
        {changeEntries.map(([field, change]: [string, { from: unknown; to: unknown }]) => (
          <div key={field} className="border rounded-lg p-3">
            <div className="font-medium text-sm mb-2">{field}</div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <div className="text-xs text-muted-foreground mb-1">From:</div>
                <div className="bg-red-50 border border-red-200 rounded p-2 text-sm">
                  <code className="text-red-800">
                    {formatJsonValue(change?.from)}
                  </code>
                </div>
              </div>
              <div>
                <div className="text-xs text-muted-foreground mb-1">To:</div>
                <div className="bg-green-50 border border-green-200 rounded p-2 text-sm">
                  <code className="text-green-800">
                    {formatJsonValue(change?.to)}
                  </code>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderMetadata = (metadata: unknown) => {
    if (!metadata || typeof metadata !== 'object') {
      return <div className="text-sm text-muted-foreground">No metadata available</div>;
    }

    const metadataEntries = Object.entries(metadata);
    if (metadataEntries.length === 0) {
      return <div className="text-sm text-muted-foreground">No metadata available</div>;
    }

    return (
      <div className="space-y-2">
        {metadataEntries.map(([key, value]) => (
          <div key={key} className="flex justify-between items-start">
            <span className="font-medium text-sm">{key}:</span>
            <span className="text-sm text-muted-foreground ml-2 text-right">
              {formatJsonValue(value)}
            </span>
          </div>
        ))}
      </div>
    );
  };

  const getActionBadgeVariant = (action: string) => {
    switch (action) {
      case "CREATE":
        return "default";
      case "UPDATE":
        return "secondary";
      case "DELETE":
        return "destructive";
      case "APPROVE":
      case "ACTIVATE":
        return "default";
      case "REJECT":
      case "DEACTIVATE":
        return "destructive";
      default:
        return "outline";
    }
  };

  const getEntityTypeColor = (entityType: string) => {
    switch (entityType) {
      case "PROPERTY":
        return "bg-blue-100 text-blue-800";
      case "PARTNER":
        return "bg-green-100 text-green-800";
      case "POST":
        return "bg-purple-100 text-purple-800";
      case "USER":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Audit Log Details</DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : auditLog ? (
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Timestamp:</span>
                    </div>
                    <div className="text-sm text-muted-foreground ml-6">
                      {format(new Date(auditLog.createdAt), "PPP pp")}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Performed By:</span>
                    </div>
                    <div className="ml-6 space-y-1">
                      <div className="text-sm">{auditLog.performedByName}</div>
                      <Badge variant="outline" className="text-xs">
                        {auditLog.performedBy.userType}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <span className="font-medium">Action:</span>
                    <div className="ml-0">
                      <Badge variant={getActionBadgeVariant(auditLog.action)}>
                        {auditLog.action}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <span className="font-medium">Entity:</span>
                    <div className="ml-0">
                      <Badge 
                        variant="outline" 
                        className={getEntityTypeColor(auditLog.entityType)}
                      >
                        {auditLog.entityType}
                      </Badge>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <span className="font-medium">Target Entity:</span>
                  <div className="space-y-1">
                    <div className="text-sm">{auditLog.entityTitle || "No title"}</div>
                    <div className="text-xs text-muted-foreground font-mono">
                      ID: {auditLog.entityId}
                    </div>
                  </div>
                </div>

                {auditLog.reason && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <span className="font-medium">Reason:</span>
                      <div className="text-sm text-muted-foreground">
                        {auditLog.reason}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Changes */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Changes
                </CardTitle>
              </CardHeader>
              <CardContent>
                {renderChanges(auditLog.changes)}
              </CardContent>
            </Card>

            {/* Metadata */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Metadata
                </CardTitle>
              </CardHeader>
              <CardContent>
                {renderMetadata(auditLog.metadata)}
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            Audit log not found
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
