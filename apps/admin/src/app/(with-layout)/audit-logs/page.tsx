"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@repo/ui/components/ui/card";
import { Button } from "@repo/ui/components/ui/button";
import { Input } from "@repo/ui/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/ui/select";
import { Badge } from "@repo/ui/components/ui/badge";
import { Calendar } from "@repo/ui/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/ui/popover";
import { CalendarIcon, Search, Filter, Download } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@repo/ui/lib/utils";
import { api } from "~/trpc/react";
import { AuditLogDataTable } from "./audit-log-data-table";
import { AuditLogStats } from "./audit-log-stats";
import { AuditLogDetailsDialog } from "./audit-log-details-dialog";

export default function AuditLogsPage() {
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<{
    entityType: string;
    action: string;
    performedById: string;
    entityId: string;
    search: string;
    dateFrom: Date | undefined;
    dateTo: Date | undefined;
  }>({
    entityType: "",
    action: "",
    performedById: "",
    entityId: "",
    search: "",
    dateFrom: undefined as Date | undefined,
    dateTo: undefined as Date | undefined,
  });
  const [selectedLogId, setSelectedLogId] = useState<string | null>(null);

  const { data: auditLogsData, isLoading } = api.auditLogs.getAuditLogs.useQuery({
    page,
    take: 20,
    // ...filters,
  });

  const { data: statsData } = api.auditLogs.getAuditLogStats.useQuery();

  const handleFilterChange = (key: string, value: string | Date | undefined) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1); // Reset to first page when filters change
  };

  const clearFilters = () => {
    setFilters({
      entityType: "",
      action: "",
      performedById: "",
      entityId: "",
      search: "",
      dateFrom: undefined,
      dateTo: undefined,
    });
    setPage(1);
  };

  const getActionBadgeVariant = (action: string) => {
    switch (action) {
      case "CREATE":
        return "default";
      case "UPDATE":
        return "secondary";
      case "DELETE":
        return "destructive";
      case "APPROVE":
      case "ACTIVATE":
        return "default";
      case "REJECT":
      case "DEACTIVATE":
        return "destructive";
      default:
        return "outline";
    }
  };

  const getEntityTypeColor = (entityType: string) => {
    switch (entityType) {
      case "PROPERTY":
        return "bg-blue-100 text-blue-800";
      case "PARTNER":
        return "bg-green-100 text-green-800";
      case "POST":
        return "bg-purple-100 text-purple-800";
      case "USER":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Audit Logs</h1>
          <p className="text-muted-foreground">
            Track all administrative actions and changes across the platform
          </p>
        </div>
        <Button variant="outline" className="gap-2">
          <Download className="h-4 w-4" />
          Export
        </Button>
      </div>

      {/* Stats Cards */}
      {statsData && <AuditLogStats stats={statsData} />}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search entity title, admin name..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Entity Type</label>
              <Select
                value={filters.entityType}
                onValueChange={(value) => handleFilterChange("entityType", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All entities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PROPERTY">Property</SelectItem>
                  <SelectItem value="PARTNER">Partner</SelectItem>
                  <SelectItem value="POST">Post</SelectItem>
                  <SelectItem value="USER">User</SelectItem>
                  <SelectItem value="COMPANY">Company</SelectItem>
                  <SelectItem value="ADMIN_USER">Admin User</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Action</label>
              <Select
                value={filters.action}
                onValueChange={(value) => handleFilterChange("action", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All actions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CREATE">Create</SelectItem>
                  <SelectItem value="UPDATE">Update</SelectItem>
                  <SelectItem value="DELETE">Delete</SelectItem>
                  <SelectItem value="APPROVE">Approve</SelectItem>
                  <SelectItem value="REJECT">Reject</SelectItem>
                  <SelectItem value="ACTIVATE">Activate</SelectItem>
                  <SelectItem value="DEACTIVATE">Deactivate</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Date From</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filters.dateFrom && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dateFrom ? format(filters.dateFrom, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.dateFrom}
                    onSelect={(date) => handleFilterChange("dateFrom", date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="flex gap-2 mt-4">
            <Button onClick={clearFilters} variant="outline" size="sm">
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Audit Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle>Audit Trail</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <AuditLogDataTable
              data={auditLogsData?.auditLogs ?? []}
              totalPages={auditLogsData?.totalPages ?? 0}
              currentPage={page}
              onPageChange={setPage}
              onViewDetails={setSelectedLogId}
              getActionBadgeVariant={getActionBadgeVariant}
              getEntityTypeColor={getEntityTypeColor}
            />
          )}
        </CardContent>
      </Card>

      {/* Details Dialog */}
      {selectedLogId && (
        <AuditLogDetailsDialog
          logId={selectedLogId}
          open={!!selectedLogId}
          onOpenChange={(open) => !open && setSelectedLogId(null)}
        />
      )}
    </div>
  );
}
