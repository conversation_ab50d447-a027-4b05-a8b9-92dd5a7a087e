"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { useState } from "react";

import { Button } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/ui/select";
import { Switch } from "@repo/ui/components/ui/switch";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import AddUpdateAdminSchema from "~/server/api/validations/admin-create.validation";
import { api } from "~/trpc/react";
import { skipToken } from "@tanstack/react-query";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { useSheet } from "~/app/hooks/use-sheet";
import { AdminParamName } from "~/app/helpers/constants";
import { AdminUserTypeEnum } from "@repo/database";
import { EyeIcon, EyeOffIcon } from "lucide-react";

const AdminAddUpdateForm = () => {
  const { paramValue: adminId, closeSheet } = useSheet(AdminParamName);
  const [visible, setVisible] = useState(false);
  // Note: I am blocking the update form from being rendered and prefetching the admin by id in page.tsx and this api gets hydrated automatically by hydrateclient. so initial load of the data will always be available from cache as i have prefetched the data in the page.tsx. thats we dont need useEffect to set the data in the form.

  const {
    data: adminDetail,
    isLoading,
    error,
  } = api.admin.getAdminById.useQuery(adminId ? { id: adminId } : skipToken, {
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const { mutate: updateAdmin, isPending: isPendingUpdateMutation } =
    api.admin.updateAdminDetails.useMutation();
  const { mutate: addAdmin, isPending: isPendingAddMutation } =
    api.admin.createNewAdmin.useMutation();
  const trpcUtils = api.useUtils();

  const form = useForm<z.infer<typeof AddUpdateAdminSchema>>({
    resolver: zodResolver(AddUpdateAdminSchema),
    defaultValues: adminDetail
      ? {
          name: adminDetail.name,
          email: adminDetail.email,
          active: adminDetail.active,
          userType: adminDetail.userType,
        }
      : {
          name: "",
          email: "",
          active: false,
          userType: undefined,
        },
  });

  const onSubmit = (values: z.infer<typeof AddUpdateAdminSchema>) => {
    if (adminId) {
      updateAdmin(
        { ...values, id: adminId },
        {
          onSuccess: (opts) => {
            toast.success(opts.message);
            void trpcUtils.admin.getAdminById.invalidate({ id: adminId });
            closeSheet();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    } else {
      addAdmin(values, {
        onSuccess: (opts) => {
          toast.success(opts.message);
          void trpcUtils.admin.getAllAdmins.invalidate();
          closeSheet();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      });
    }
  };

  if (isLoading && adminId) {
    return <div className="p-4 text-center">Loading admin details...</div>;
  }

  if (error && adminId) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading admin: {error.message}
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel className="text-sm font-medium">Name</FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Enter name"
                  className="w-full"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel className="text-sm font-medium">Email</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="Enter email"
                  className="w-full"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="userType"
          render={({ field }) => (
            <FormItem className="w-full space-y-1 md:w-[260px] lg:w-[340px] xl:w-[436px] 2xl:w-[650px]">
              <FormLabel>User Type</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select user type" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(AdminUserTypeEnum).map((userType) => (
                      <SelectItem key={userType} value={userType}>
                        {userType}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {adminId ? <></> : (
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel className="text-sm font-medium">Password</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type={visible ? "text" : "password"}
                      placeholder="password"
                      className="pr-11"
                      {...field}
                    />
                    <div className="absolute right-3 top-4">
                      {visible ? (
                        <EyeIcon
                          onClick={() => setVisible(!visible)}
                          className="cursor-pointer"
                        />
                      ) : (
                        <EyeOffIcon
                          onClick={() => setVisible(!visible)}
                          className="cursor-pointer"
                        />
                      )}
                    </div>
                  </div>
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        )}
        <FormField
          control={form.control}
          name="active"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel className="text-sm font-medium">Active</FormLabel>
              <FormControl>
                <Switch
                  className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-red-500"
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />

        {adminId && isPendingUpdateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Updating...
          </LoadingButton>
        ) : (
          adminId && (
            <Button type="submit" className="w-full py-3">
              {adminId ? "Update" : "Create"}
            </Button>
          )
        )}

        {!adminId && isPendingAddMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Creating...
          </LoadingButton>
        ) : (
          !adminId && (
            <Button type="submit" className="w-full py-3">
              {adminId ? "Update" : "Create"}
            </Button>
          )
        )}
      </form>
    </Form>
  );
};

export default AdminAddUpdateForm;
