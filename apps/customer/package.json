{"name": "customer", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack -p 3001", "lint": "next lint", "start": "next start -p 7001", "build": "next build"}, "dependencies": {"@auth/prisma-adapter": "^1.6.0", "@prisma/nextjs-monorepo-workaround-plugin": "6.8.2", "@repo/customer-api": "*", "@repo/customer-auth": "*", "@repo/database": "*", "@repo/tailwind-config": "*", "@repo/ui": "*", "@repo/validators": "*", "@sentry/nextjs": "10.9.0", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5", "@tanstack/react-query-devtools": "^5", "@trpc/client": "^11.2.0", "@trpc/next": "^11.2.0", "@trpc/react-query": "^11.2.0", "@trpc/server": "^11.2.0", "@trpc/tanstack-react-query": "^11.2.0", "@uidotdev/usehooks": "^2.4.1", "cloudinary": "^2.6.0", "date-fns": "^3.6.0", "geist": "^1.3.0", "lucide-react": "^0.395.0", "next": "15.5.2", "next-auth": "5.0.0-beta.25", "next-cloudinary": "^6.16.0", "react": "19.1.0", "react-dom": "19.1.0", "react-intersection-observer": "^9.13.1", "server-only": "^0.0.1", "sharp": "^0.33.5", "superjson": "^2.2.1", "zod": "^3.24.1"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/tsconfig": "*", "@types/eslint": "^8.56.10", "@types/node": "20.19.12", "@types/react": "19.1.12", "@types/react-dom": "19.1.9", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "autoprefixer": "^10.4.19", "eslint": "^9.12.0", "eslint-config-next": "15.5.2", "postcss": "^8.4.47", "postcss-load-config": "^6.0.1", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.15", "typescript": "^5.8.2"}, "ct3aMetadata": {"initVersion": "7.38.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "resolutions": {"@types/react": "19.1.12", "@types/react-dom": "19.1.9"}}