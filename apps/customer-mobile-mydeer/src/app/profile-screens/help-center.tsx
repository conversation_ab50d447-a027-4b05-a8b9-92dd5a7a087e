import { View, Text, Pressable, <PERSON>ing, <PERSON><PERSON>, <PERSON><PERSON>View } from 'react-native';
import React, { useState } from 'react';
import Entypo from '@expo/vector-icons/Entypo';
import { Image } from 'expo-image';

const HeadingWithIcon = ({ heading }: { heading: string }) => {
  return (
    <View className="self-center px-4 py-1.5 flex-row items-center justify-center bg-secondary-100 rounded-lg">
      <Image
        source={require('@assets/icons/helpcenter1.png')}
        className="mr-2.5 h-4 w-4"
      />
      <Text className="text-primary-750 font-medium text-xs font-airbnb_md">
        {heading}
      </Text>
    </View>
  );
};

const FAQQuestionComponent = ({
  Question,
  Answer,
}: {
  Question: string;
  Answer: string;
}) => {
  const [expand, setExpand] = useState(false);
  const ToggleExpand = () => {
    setExpand((prev) => !prev);
  };
  return (
    <Pressable
      className={`mb-5 p-5 border rounded-lg border-[#CFC1B6] ${expand && 'bg-[#FFFCF4]'}`}
      onPress={ToggleExpand}
    >
      <View className="flex-row items-center justify-between">
        <Text className="text-base font-airbnb_md font-medium text-primary-750 ">
          {Question}
        </Text>
        <Entypo
          name={expand ? 'chevron-thin-up' : 'chevron-thin-down'}
          size={20}
          color="#784100"
          onPress={ToggleExpand}
        />
      </View>
      {expand && (
        <Text className="mt-4 text-sm font-normal font-airbnb_bk text-text-550">
          {Answer}
        </Text>
      )}
    </Pressable>
  );
};

type HelpCenterProps = {
  email: string;
  phoneNumber: string;
};

const HelpCenter: React.FC<HelpCenterProps> = () => {
  const email = '<EMAIL>';
  const phoneNumber = '+91 7043807438';
  const handleEmailPress = async () => {
    try {
      const supported = await Linking.canOpenURL(`mailto:${email}`);
      if (!supported) {
        Alert.alert('Email is not supported on this device.');
        return;
      }
      await Linking.openURL(`mailto:${email}`);
    } catch (error) {
      Alert.alert('An error occurred while opening the email app.');
      console.log(error);
    }
  };

  const handlePhonePress = async () => {
    try {
      const supported = await Linking.canOpenURL(`tel:${phoneNumber}`);
      if (!supported) {
        Alert.alert('Phone calls are not supported on this device.');
        return;
      }
      await Linking.openURL(`tel:${phoneNumber}`);
    } catch (error) {
      Alert.alert('An error occurred while opening the phone app.');
      console.log(error);
    }
  };

  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <View className="flex-1 px-5 pb-5">
        {/* help & support */}
        <View className="mt-4 border border-secondary-100 p-4 rounded-3xl">
          <HeadingWithIcon heading="Help & Support" />

          <Text className="mt-2 text-center text-sm font-normal font-airbnb_bk text-text-600">
            Feel free to reach out to us with any inquiries, feedback, or
            requests. Our dedicated team is committed to providing you with
            exceptional customer service. We look forward to hearing from you!
          </Text>

          <Pressable className="mt-5 mb-2 flex-row" onPress={handleEmailPress}>
            <View className="mr-4 bg-secondary-100 rounded-full p-2 self-start">
              <Image
                source={require('@assets/icons/email.png')}
                className="h-6 w-6"
              />
            </View>

            <View>
              <Text className="text-primary-750 text-base font-airbnb_md font-medium">
                Email
              </Text>
              <Text className="mt-1 text-base font-medium font-airbnb_md text-text-550">
                <EMAIL>
              </Text>
            </View>
          </Pressable>

          <Pressable className="flex-row" onPress={handlePhonePress}>
            <View className="mr-4 bg-secondary-100 rounded-full p-2 self-start">
              <Image
                source={require('@assets/icons/phonenumber.png')}
                className="h-6 w-6"
              />
            </View>
            <View>
              <Text className="text-primary-750 text-base font-airbnb_md font-medium">
                Contact Number
              </Text>
              <Text className="mt-1 text-base font-medium font-airbnb_md text-text-550">
                +91 7043807438
              </Text>
            </View>
          </Pressable>
        </View>

        {/* FAQ */}
        <View className="mt-5">
          <View className="mb-4 items-center justify-center">
            <HeadingWithIcon heading="FAQ" />
            <Text className="mt-2 text-xl font-airbnb_bd font-bold text-text-main700">
              You Have Question. We’ve Your Answer
            </Text>
          </View>

          <FAQQuestionComponent
            Question={'How we can connect with an agent ?'}
            Answer={
              'Click on the agent profile card that has been shown on agent profile page and click on connect button to show your interest in agent properties.'
            }
          />
          <FAQQuestionComponent
            Question={'What is the basic plan for an agent to post property ?'}
            Answer={
              'Click on the agent profile card that has been shown on agent profile page and click on connect button to show your interest in agent properties.'
            }
          />
          <FAQQuestionComponent
            Question={'What is the basic plan for an agent to post property ?'}
            Answer={
              'Click on the agent profile card that has been shown on agent profile page and click on connect button to show your interest in agent properties.'
            }
          />
          <FAQQuestionComponent
            Question={'What is the basic plan for an agent to post property?'}
            Answer={
              'Click on the agent profile card that has been shown on agent profile page and click on connect button to show your interest in agent properties.'
            }
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default HelpCenter;
