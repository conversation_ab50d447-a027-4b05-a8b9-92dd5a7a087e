import { View, Text, ActivityIndicator, Platform } from 'react-native';
import React, { forwardRef } from 'react';
import { FocusAwareStatusBar, Modal } from '@/components/ui';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { BlurView } from 'expo-blur';
import { PropertyDetailsPage } from '@/components/routes/property-details-page-component/property-details-page';
import { api } from '@/utils/api';
import { PropertyDetailsProps } from '@/types';
import { ScrollView } from 'react-native-gesture-handler';

export interface PropertyDetailsModalProps {
  refModal: React.RefObject<BottomSheetModal | null>;
  onClose: () => void;
  position: number;
  updatePosition: (val: number) => void;
  id: string;
}

const PropertyDetailsModal = forwardRef<
  BottomSheetModal,
  PropertyDetailsModalProps
>(({ onClose, updatePosition, id }, refModal) => {
  const {
    data: propertyData,
    isLoading: isLoadingPropertyData,
    isError: isErrorPropertyData,
  } = api.property.getPropertyDetails.useQuery(
    {
      id,
    },
    { enabled: !!id }
  );

  const renderResults = () => {
    if (isLoadingPropertyData) {
      return (
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size={40} color="#fff" />
        </View>
      );
    } else if (isErrorPropertyData || !propertyData) {
      return (
        <View className="flex-1 items-center justify-center">
          <Text>Error loading Property Data</Text>
        </View>
      );
    }
  };

  return (
    <Modal
      backgroundStyle={{ backgroundColor: 'rgba(255, 248, 244, 0.80)' }}
      containerStyle={{ marginHorizontal: 20, marginBottom: 20 }}
      ref={refModal}
      snapPoints={['60%']}
      onChange={(index, position, type) => {
        updatePosition(index);
      }}
      onDismiss={onClose}
      enablePanDownToClose={true}
      enableDynamicSizing={true}
    >
      {renderResults()}
      <BlurView
        experimentalBlurMethod="dimezisBlurView"
        intensity={40}
        className="flex-1 rounded-2xl pb-10 bg-white"
      >
        {propertyData && (
          <ScrollView
            className="flex-1"
            style={{ flex: 1 }}
            showsVerticalScrollIndicator={false}
          >
            <PropertyDetailsPage
              item={propertyData as PropertyDetailsProps}
              dismissModal={onClose}
            />
          </ScrollView>
        )}
      </BlurView>
    </Modal>
  );
});

export default PropertyDetailsModal;
