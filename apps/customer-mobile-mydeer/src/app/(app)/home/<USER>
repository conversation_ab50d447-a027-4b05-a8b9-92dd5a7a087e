import { Image as NImage } from 'expo-image';
import React, { useState, useCallback, useRef } from 'react';
import {
  Text,
  View,
  Pressable,
  ActivityIndicator,
  RefreshControl,
  ScrollView,
} from 'react-native';

import { useModal } from '@/components/ui';
import PropertySwiperList, { type PropertySwiperListRef } from '@/components/shared/property-swiper-list';
import PropertyCard from '@/components/shared/property-card';
import ErrorBoundary from '@/components/shared/error-boundary';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import PropertyDetailsModal from './property-details-modal';
import { api } from '@/utils/api';
import { usePreferenceStore } from '@/lib';

export default function Home() {
  const { userPreference: storedData } = usePreferenceStore();

  const {
    data: filteredData,
    isLoading: isInitialLoading,
    isError,
    refetch,
  } = api.property.getPropertiesAccordingToFilters.useQuery(
    {
      propertyFor: storedData.propertyFor,
      amenities: storedData.amenities,
      baths: storedData.baths,
      beds: storedData.beds,
      facing: storedData.facing,
      furnishType: storedData.furnishType,
      homeTypes: storedData.homeTypes,
      listingTypes: storedData.listingTypes,
      maxArea: storedData.maxArea,
      stayType: storedData.stayType,
      propertyState: storedData.propertyState,
      rentAmenities: storedData.rentAmenities,
      possessionState: storedData.possessionState,
      pointOfInterests: storedData.pointOfInterests,
      minPrice: storedData.minPrice,
      minArea: storedData.minArea,
      maxPrice: storedData.maxPrice,
      searchQuery: storedData.searchQuery,
      propertyCategory: storedData.propertyCategory,
      take: 10, // Reduced take for more frequent pagination
      page: 1,
      areaUnitId: '',
    },
    {
      enabled: !!storedData && !!storedData.propertyFor,
    }
  );

  const [position, setPosition] = useState(-1);
  const insets = useSafeAreaInsets();
  const modal = useModal();
  const [propertyId, setPropertyId] = useState<string>();
  const [swiperError, setSwiperError] = useState(false);
  const likePropertyMutation =
    api.user.handleAddRemoveFavouriteProperty.useMutation();
  const utils = api.useUtils();
  const swiperRef = useRef<PropertySwiperListRef>(null);

  const handleOpenBottomSheet = (id: string) => {
    setPropertyId(id);
    modal.present();
  };

  const handleCloseBottomSheet = () => {
    modal.dismiss();
  };

  const OverlayLabelRight = useCallback(() => {
    try {
      return (
        <View className="w-full h-full rounded-2xl bg-green-500 opacity-80 items-center justify-center">
          <Text className="text-white text-2xl font-bold">LIKE</Text>
        </View>
      );
    } catch (error) {
      console.warn('Error rendering OverlayLabelRight:', error);
      return null;
    }
  }, []);

  const OverlayLabelLeft = useCallback(() => {
    try {
      return (
        <View className="w-full h-full rounded-2xl bg-red-500 opacity-80 items-center justify-center">
          <Text className="text-white text-2xl font-bold">PASS</Text>
        </View>
      );
    } catch (error) {
      console.warn('Error rendering OverlayLabelLeft:', error);
      return null;
    }
  }, []);

  const isLoading = isInitialLoading;

  return (
    <View style={{ flex: 1 }} className="bg-[#F4EFED]">
      <ScrollView
        showsVerticalScrollIndicator={false}
        bounces={true}
        contentContainerStyle={{ flexGrow: 1 }}
        className="flex-1"
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refetch}
            progressViewOffset={insets.top + 60}
          />
        }
      >
        {/* Header */}
        <View
          style={{
            paddingTop: insets.top,
            position: 'sticky',
            top: 0,
            zIndex: 10,
          }}
          className="bg-non"
        >
          <View className="mb-3 px-5 flex-row items-center justify-between">
            <NImage
              source={require('@assets/images/mydeer1.png')}
              style={{ width: 86.5, height: 51.75 }}
              contentFit="contain"
            />
            <View className="flex-row items-center gap-2.5">
              {/* <FilterModal /> */}
            </View>
          </View>
        </View>

        {/* Content */}
        {isLoading ? (
          <View className="flex-1 items-center justify-center">
            <ActivityIndicator size={40} color="#000" />
          </View>
        ) : isError ? (
          <View className="flex-1 items-center justify-center">
            <Text>Error loading Properties</Text>
            <Pressable
              className="mt-4 px-4 py-2 bg-primary-700 rounded-md"
              onPress={() => refetch()}
            >
              <Text className="text-white">Try Again</Text>
            </Pressable>
          </View>
        ) : !filteredData?.properties.length ? (
          <View className="flex-1 items-center justify-center">
            <Text className="text-xl font-semi-bold">No properties found</Text>
            <Text className="mt-2 text-sm text-gray-500">
              Pull down to refresh
            </Text>
          </View>
        ) : filteredData?.properties && filteredData.properties.length > 0 ? (
          <View className="flex-1 items-center px-5">
            {swiperError ? (
              <View className="flex-1 items-center justify-center">
                <Text className="text-gray-500 text-lg mb-4">Swiper temporarily unavailable</Text>
                <Text className="text-gray-400 text-sm text-center px-4">
                  Please restart the app or contact support if this persists
                </Text>
              </View>
            ) : (
              <ErrorBoundary
                fallback={({ error }) => (
                  <View className="flex-1 items-center justify-center">
                    <Text className="text-gray-500 text-lg mb-4">Swiper Error</Text>
                    <Text className="text-gray-400 text-sm text-center px-4">
                      {error?.message || 'An unexpected error occurred'}
                    </Text>
                  </View>
                )}
              >
                <PropertySwiperList
                  ref={swiperRef}
                  data={filteredData.properties}
                  renderCard={(property) => {
                    try {
                      return (
                        <PropertyCard
                          property={property}
                          onPress={handleOpenBottomSheet}
                        />
                      );
                    } catch (error) {
                      console.warn('Error rendering property card:', error);
                      setSwiperError(true);
                      return null;
                    }
                  }}
                  onSwipeRight={(property) => {
                    if (property?.id) {
                      try {
                        likePropertyMutation.mutate({
                          propertyId: property.id,
                        });
                        utils.user.invalidate();
                      } catch (error) {
                        console.warn('Error liking property:', error);
                      }
                    }
                  }}
                  onSwipeLeft={(property) => {
                    if (__DEV__) {
                      console.log('Swiped left on property:', property?.id);
                    }
                  }}
                  onSwipeComplete={(direction, property, index) => {
                    if (__DEV__) {
                      console.log('Swipe completed:', direction, 'property:', property?.id, 'index:', index);
                    }
                  }}
                  OverlayLabelRight={OverlayLabelRight}
                  OverlayLabelLeft={OverlayLabelLeft}
                />
              </ErrorBoundary>
            )}
          </View>
        ) : (
          <View className="flex-1 items-center justify-center px-5">
            <Text className="text-gray-500 text-lg">No properties available</Text>
          </View>
        )}
      </ScrollView>

      {/* Modal */}
      <View className="pb-[100px]">
        <PropertyDetailsModal
          position={position}
          refModal={modal.ref}
          updatePosition={setPosition}
          ref={modal.ref}
          onClose={handleCloseBottomSheet}
          id={propertyId ?? ''}
        />
      </View>
    </View>
  );
}


