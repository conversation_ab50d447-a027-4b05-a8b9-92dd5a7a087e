import React, { memo, useMemo } from 'react';
import { View, Text, Pressable } from 'react-native';
import { Image as NImage } from 'expo-image';
import type { IProperty } from '@/types';

interface PropertyCardProps {
  property: IProperty;
  onPress?: (propertyId: string) => void;
  style?: object;
}

const PropertyCard = ({ property, onPress, style }: PropertyCardProps) => {
  // Memoize property images to avoid recalculation
  const propertyImages = useMemo(() => {
    return property.mediaSections?.flatMap((media) => media.media) || [];
  }, [property.mediaSections]);

  // Memoize the first image URL
  const firstImageUrl = useMemo(() => {
    if (propertyImages.length > 0) {
      return (
        propertyImages[0].cloudinaryUrl ??
        propertyImages[0].filePublicUrl ??
        null
      );
    }
    return null;
  }, [propertyImages]);

  // Memoize formatted price
  const formattedPrice = useMemo(() => {
    if (property?.propertyPrice) {
      return `₹${Number(property.propertyPrice).toLocaleString()}`;
    }
    return 'Price N/A';
  }, [property?.propertyPrice]);

  // Memoize price per square meter
  const pricePerSqMeter = useMemo(() => {
    if (property?.propertyPrice && property?.areaInSqMeters) {
      const pricePerSqMt = Math.round(
        Number(property.propertyPrice) / Number(property.areaInSqMeters)
      );
      return `₹${pricePerSqMt.toLocaleString()}/sq.mt`;
    }
    return '';
  }, [property?.propertyPrice, property?.areaInSqMeters]);

  const handlePress = () => {
    if (property?.id && onPress) {
      onPress(property.id);
    }
  };

  return (
    <Pressable
      className="flex-1 rounded-2xl overflow-hidden"
      style={style}
      onPress={handlePress}
    >
      <View className="flex-1 relative">
        {firstImageUrl ? (
          <NImage
            source={{ uri: firstImageUrl }}
            className="w-full h-full rounded-2xl"
            contentFit="cover"
            placeholder={require('@assets/images/Card.png')}
            placeholderContentFit="cover"
            transition={200}
            cachePolicy="memory-disk"
            priority="high"
          />
        ) : (
          <NImage
            source={require('@assets/images/Card.png')}
            className="w-full h-full rounded-2xl"
            contentFit="cover"
          />
        )}

        {/* Property Details Overlay */}
        <View className="absolute bottom-0 left-0 right-0 px-4 pb-8">
          <View className="bg-white rounded-2xl p-4 shadow-md">
            <View className="flex-row justify-between items-start mb-3">
              <View className="flex-1 mr-3">
                <Text
                  className="text-base font-bold text-gray-900 font-airbnb_bd"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {property?.propertyTitle ?? 'No Title'}
                </Text>
                <View className="flex-row items-center mt-0.5">
                  <NImage
                    source={require('@assets/icons/location2.png')}
                    className="w-2.5 h-2.5"
                  />
                  <Text
                    className="ml-1 text-xs text-gray-600 font-airbnb_bk"
                    numberOfLines={1}
                    ellipsizeMode="tail"
                  >
                    {property?.propertyLocation ?? 'Location Not Available'}
                  </Text>
                </View>
              </View>

              <View className="items-end">
                <Text
                  className="text-lg font-extrabold text-primary-700 text-right font-airbnb_xbd"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {formattedPrice}
                </Text>
                {pricePerSqMeter && (
                  <Text
                    className="text-sm font-extrabold text-primary-700 text-right font-airbnb_xbd"
                    numberOfLines={1}
                    ellipsizeMode="tail"
                  >
                    {pricePerSqMeter}
                  </Text>
                )}
              </View>
            </View>

            <Text
              className="text-xs text-gray-600 leading-3.5 font-airbnb_bk"
              numberOfLines={5}
              ellipsizeMode="tail"
            >
              {property?.aboutProperty ?? 'No description available'}
            </Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
};



// Custom comparison function for better memoization
const arePropsEqual = (prevProps: PropertyCardProps, nextProps: PropertyCardProps) => {
  return (
    prevProps.property.id === nextProps.property.id &&
    prevProps.property.propertyTitle === nextProps.property.propertyTitle &&
    prevProps.property.propertyPrice === nextProps.property.propertyPrice &&
    prevProps.property.propertyLocation === nextProps.property.propertyLocation &&
    prevProps.property.aboutProperty === nextProps.property.aboutProperty &&
    prevProps.property.areaInSqMeters === nextProps.property.areaInSqMeters &&
    prevProps.property.mediaSections?.length === nextProps.property.mediaSections?.length
  );
};

PropertyCard.displayName = 'PropertyCard';

export default memo(PropertyCard, arePropsEqual);
