import React, { useState, useCallback, useImperativeHandle, forwardRef, useMemo } from 'react';
import { Dimensions, View, Text, PanResponder } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  interpolate,
  Extrapolation,
} from 'react-native-reanimated';
import type { IProperty } from '@/types';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Constants
const SWIPE_THRESHOLD = SCREEN_WIDTH * 0.25;
const ROTATION_ANGLE = 15;
const CARD_WIDTH = Math.min(SCREEN_WIDTH * 0.9, 425);
const CARD_HEIGHT = SCREEN_HEIGHT * 0.75;

const SPRING_CONFIG = {
  damping: 15,
  stiffness: 150,
  mass: 1,
};

export interface PropertySwiperListRef {
  swipeLeft: () => void;
  swipeRight: () => void;
  reset: () => void;
}

interface PropertySwiperListProps {
  data: IProperty[];
  renderCard: (item: IProperty, index: number) => React.ReactNode;
  onSwipeLeft?: (item: IProperty, index: number) => void;
  onSwipeRight?: (item: IProperty, index: number) => void;
  onSwipeComplete?: (direction: 'left' | 'right', item: IProperty, index: number) => void;
  OverlayLabelLeft?: React.ComponentType;
  OverlayLabelRight?: React.ComponentType;
  cardStyle?: object;
}

const PropertySwiperList = forwardRef<PropertySwiperListRef, PropertySwiperListProps>(
  (
    {
      data,
      renderCard,
      onSwipeLeft,
      onSwipeRight,
      onSwipeComplete,
      OverlayLabelLeft,
      OverlayLabelRight,
      cardStyle,
    },
    ref
  ) => {
    const [currentCardIndex, setCurrentCardIndex] = useState(0);
    const [isAnimating, setIsAnimating] = useState(false);

    // Animation values for current card
    const currentCardTranslateX = useSharedValue(0);
    const currentCardTranslateY = useSharedValue(0);
    const currentCardScale = useSharedValue(1);
    const currentCardRotation = useSharedValue(0);

    // Animation values for next card (background card)
    const nextCardScale = useSharedValue(0.8);
    const nextCardOpacity = useSharedValue(1);

    // Get current and next cards for 2-card system
    const { currentCard, nextCard, backgroundCard } = useMemo(() => {
      if (!data || data.length === 0) {
        return { currentCard: null, nextCard: null, backgroundCard: null };
      }
      const index = Math.max(0, currentCardIndex);
      return {
        currentCard: index < data.length ? data[index] : null,
        nextCard: index + 1 < data.length ? data[index + 1] : null,
        backgroundCard: index + 2 < data.length ? data[index + 2] : null,
      };
    }, [data, currentCardIndex]);

    // Reset current card to center position
    const resetCurrentCard = useCallback(() => {
      currentCardTranslateX.value = withSpring(0, SPRING_CONFIG);
      currentCardTranslateY.value = withSpring(0, SPRING_CONFIG);
      currentCardScale.value = withSpring(1, SPRING_CONFIG);
      currentCardRotation.value = withSpring(0, SPRING_CONFIG);
    }, [currentCardTranslateX, currentCardTranslateY, currentCardScale, currentCardRotation]);

    // Handle swipe completion and move to next card
    const handleSwipeComplete = useCallback(
      (direction: 'left' | 'right') => {
        if (isAnimating) return; // Prevent multiple swipes

        const currentItem = data[currentCardIndex];
        if (currentItem) {
          // Call callbacks immediately
          if (direction === 'left') {
            onSwipeLeft?.(currentItem, currentCardIndex);
          } else {
            onSwipeRight?.(currentItem, currentCardIndex);
          }
          onSwipeComplete?.(direction, currentItem, currentCardIndex);
        }
      },
      [data, currentCardIndex, onSwipeLeft, onSwipeRight, onSwipeComplete, isAnimating]
    );

    // Move to next card (called after animation completes)
    const moveToNextCard = useCallback(() => {
      if (currentCardIndex < data.length - 1) {
        // Move to next card
        setCurrentCardIndex(prev => prev + 1);
        // Reset current card position for next card
        currentCardTranslateX.value = 0;
        currentCardTranslateY.value = 0;
        currentCardScale.value = 1;
        currentCardRotation.value = 0;
        // Animate next card to full scale
        nextCardScale.value = withSpring(1, SPRING_CONFIG);
        setIsAnimating(false);
      } else {
        // We've reached the end - move past the last card to trigger "No more properties"
        setCurrentCardIndex(prev => prev + 1);
        setIsAnimating(false);
      }
    }, [currentCardIndex, data.length, currentCardTranslateX, currentCardTranslateY, currentCardScale, currentCardRotation, nextCardScale]);

    // Swipe card animation with proper removal
    const swipeCard = useCallback(
      (direction: 'left' | 'right') => {
        if (isAnimating) return; // Prevent multiple swipes

        setIsAnimating(true);
        const targetX = direction === 'right' ? SCREEN_WIDTH * 1.5 : -SCREEN_WIDTH * 1.5;
        const targetRotation = direction === 'right' ? ROTATION_ANGLE : -ROTATION_ANGLE;

        // Handle swipe completion immediately
        handleSwipeComplete(direction);

        // Animate current card out
        currentCardTranslateX.value = withTiming(targetX, { duration: 300 });
        currentCardRotation.value = withTiming(targetRotation, { duration: 300 });
        currentCardScale.value = withTiming(0.8, { duration: 300 });

        // Animate next card to foreground
        nextCardScale.value = withSpring(0.9, SPRING_CONFIG);

        // Move to next card after animation
        setTimeout(() => {
          moveToNextCard();
        }, 350);
      },
      [currentCardTranslateX, currentCardRotation, currentCardScale, nextCardScale, handleSwipeComplete, moveToNextCard, isAnimating]
    );

    // Pan responder for gesture handling
    const panResponder = PanResponder.create({
      onMoveShouldSetPanResponder: () => !isAnimating,
      onPanResponderGrant: () => {
        // Gesture started - prepare for interaction
      },
      onPanResponderMove: (_, gestureState) => {
        if (isAnimating) return;

        currentCardTranslateX.value = gestureState.dx;
        currentCardTranslateY.value = gestureState.dy;

        // Rotation based on horizontal movement
        const rotationValue = interpolate(
          gestureState.dx,
          [-SCREEN_WIDTH, 0, SCREEN_WIDTH],
          [-ROTATION_ANGLE, 0, ROTATION_ANGLE],
          Extrapolation.CLAMP
        );
        currentCardRotation.value = rotationValue;

        // Scale effect
        const scaleValue = interpolate(
          Math.abs(gestureState.dx),
          [0, SWIPE_THRESHOLD],
          [1, 0.95],
          Extrapolation.CLAMP
        );
        currentCardScale.value = scaleValue;

        // Scale up next card as current card moves
        const nextScaleValue = interpolate(
          Math.abs(gestureState.dx),
          [0, SWIPE_THRESHOLD],
          [0.8, 0.9],
          Extrapolation.CLAMP
        );
        nextCardScale.value = nextScaleValue;
      },
      onPanResponderRelease: (_, gestureState) => {
        if (isAnimating) return;

        const shouldSwipe = Math.abs(gestureState.dx) > SWIPE_THRESHOLD;

        if (shouldSwipe) {
          const direction = gestureState.dx > 0 ? 'right' : 'left';
          swipeCard(direction);
        } else {
          resetCurrentCard();
          // Reset next card scale
          nextCardScale.value = withSpring(0.8, SPRING_CONFIG);
        }
      },
    });

    // Animated styles for current card
    const currentCardAnimatedStyle = useAnimatedStyle(() => ({
      transform: [
        { translateX: currentCardTranslateX.value },
        { translateY: currentCardTranslateY.value },
        { scale: currentCardScale.value },
        { rotate: `${currentCardRotation.value}deg` },
      ],
    }));

    // Animated styles for next card (background)
    const nextCardAnimatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: nextCardScale.value }],
      opacity: nextCardOpacity.value,
    }));

    // Overlay styles for swipe feedback
    const leftOverlayStyle = useAnimatedStyle(() => ({
      opacity: interpolate(
        currentCardTranslateX.value,
        [-SWIPE_THRESHOLD, 0],
        [1, 0],
        Extrapolation.CLAMP
      ),
    }));

    const rightOverlayStyle = useAnimatedStyle(() => ({
      opacity: interpolate(
        currentCardTranslateX.value,
        [0, SWIPE_THRESHOLD],
        [0, 1],
        Extrapolation.CLAMP
      ),
    }));

    // Imperative methods
    useImperativeHandle(ref, () => ({
      swipeLeft: () => swipeCard('left'),
      swipeRight: () => swipeCard('right'),
      reset: () => {
        setCurrentCardIndex(0);
        setIsAnimating(false);
        nextCardScale.value = 0.8;
        nextCardOpacity.value = 1;
        resetCurrentCard();
      },
    }), [swipeCard, resetCurrentCard, nextCardScale, nextCardOpacity]);

    // Show "No more properties" when we've gone through all cards
    if (!data.length) {
      return (
        <View className="flex-1 items-center justify-center px-4">
          <Text className="text-gray-500 text-lg">No properties available</Text>
        </View>
      );
    }

    // Show "No more properties" when current index exceeds available cards
    if (currentCardIndex >= data.length) {
      return (
        <View className="flex-1 items-center justify-center px-4">
          <Text className="text-gray-500 text-lg">No more properties</Text>
          <Text className="text-gray-400 text-sm mt-2">You've viewed all available properties</Text>
        </View>
      );
    }

    // If no current card but we haven't exceeded the data length, something went wrong
    if (!currentCard) {
      return (
        <View className="flex-1 items-center justify-center px-4">
          <Text className="text-gray-500 text-lg">Loading properties...</Text>
        </View>
      );
    }

    return (
      <View className="flex-1 items-center justify-center px-4" style={{ paddingTop: 20, paddingBottom: 20 }}>
        {/* Background card (third card) - static */}
        {backgroundCard && (
          <Animated.View
            key={`background-${backgroundCard.id}-${currentCardIndex + 2}`}
            className="absolute bg-white rounded-2xl shadow-sm"
            style={[
              {
                width: CARD_WIDTH,
                height: CARD_HEIGHT,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.1,
                shadowRadius: 1,
                elevation: 1,
                zIndex: 0,
                transform: [{ scale: 0.75 }],
              },
              cardStyle,
            ]}
          >
            {renderCard(backgroundCard, currentCardIndex + 2)}
          </Animated.View>
        )}

        {/* Next card (behind current card) - animated */}
        {nextCard && (
          <Animated.View
            key={`next-${nextCard.id}-${currentCardIndex + 1}`}
            className="absolute bg-white rounded-2xl shadow-md"
            style={[
              {
                width: CARD_WIDTH,
                height: CARD_HEIGHT,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.15,
                shadowRadius: 2,
                elevation: 3,
                zIndex: 1,
              },
              cardStyle,
              nextCardAnimatedStyle,
            ]}
          >
            {renderCard(nextCard, currentCardIndex + 1)}
          </Animated.View>
        )}

        {/* Current card (on top) - fully interactive */}
        <Animated.View
          key={`current-${currentCard.id}-${currentCardIndex}`}
          className="absolute bg-white rounded-2xl shadow-lg"
          style={[
            {
              width: CARD_WIDTH,
              height: CARD_HEIGHT,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: 5,
              zIndex: 2,
            },
            cardStyle,
            currentCardAnimatedStyle,
          ]}
          {...panResponder.panHandlers}
        >
          {/* Render the card content */}
          {renderCard(currentCard, currentCardIndex)}

          {/* Overlay labels for swipe feedback */}
          {OverlayLabelLeft && (
            <Animated.View
              className="absolute inset-0 rounded-2xl"
              style={leftOverlayStyle}
            >
              <OverlayLabelLeft />
            </Animated.View>
          )}
          {OverlayLabelRight && (
            <Animated.View
              className="absolute inset-0 rounded-2xl"
              style={rightOverlayStyle}
            >
              <OverlayLabelRight />
            </Animated.View>
          )}
        </Animated.View>
      </View>
    );
  }
);

PropertySwiperList.displayName = 'PropertySwiperList';

export default PropertySwiperList;
