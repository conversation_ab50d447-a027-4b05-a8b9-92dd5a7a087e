# PropertySwiperList Component

A high-performance Tinder-like swiper component built with React Native Reanimated 4, optimized for property cards with smooth animations and gesture handling.

## Features

- ✅ **High Performance**: Built with React Native Reanimated 4 for 60fps animations
- ✅ **Tinder-like Experience**: Shows one card at a time for focused interaction
- ✅ **Zoom Effect**: Next card appears with smooth zoom animation (small to large)
- ✅ **Responsive Design**: Maximum card width of 425px for optimal viewing
- ✅ **No Clipping**: Proper container padding prevents card clipping
- ✅ **Gesture Support**: Smooth pan gestures with spring animations
- ✅ **Memory Optimized**: Efficient memory management with single card rendering
- ✅ **State Management**: Avoids Reanimated warnings with proper state handling
- ✅ **Customizable**: Support for custom overlay labels and card styles
- ✅ **TypeScript**: Full TypeScript support with proper type definitions
- ✅ **Imperative API**: Programmatic control with ref methods

## Usage

### Basic Implementation

```tsx
import PropertySwiperList, { PropertySwiperListRef } from '@/components/shared/property-swiper-list';
import PropertyCard from '@/components/shared/property-card';

const MyComponent = () => {
  const swiperRef = useRef<PropertySwiperListRef>(null);

  return (
    <PropertySwiperList
      ref={swiperRef}
      data={properties}
      renderCard={(property) => (
        <PropertyCard
          property={property}
          onPress={handlePropertyPress}
        />
      )}
      onSwipeRight={(property) => {
        // Handle like/favorite action
        console.log('Liked property:', property.id);
      }}
      onSwipeLeft={(property) => {
        // Handle dislike/pass action
        console.log('Passed property:', property.id);
      }}
      OverlayLabelRight={LikeOverlay}
      OverlayLabelLeft={PassOverlay}
    />
  );
};
```

### Custom Overlay Components

```tsx
const LikeOverlay = () => (
  <View className="w-full h-full rounded-2xl bg-green-500 opacity-80 items-center justify-center">
    <Text className="text-white text-2xl font-bold">
      LIKE
    </Text>
  </View>
);

const PassOverlay = () => (
  <View className="w-full h-full rounded-2xl bg-red-500 opacity-80 items-center justify-center">
    <Text className="text-white text-2xl font-bold">
      PASS
    </Text>
  </View>
);
```

### Imperative Methods

```tsx
// Programmatically swipe cards
swiperRef.current?.swipeLeft();
swiperRef.current?.swipeRight();
swiperRef.current?.reset();
```

## Props

### PropertySwiperList Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `data` | `IProperty[]` | ✅ | Array of property objects |
| `renderCard` | `(item: IProperty, index: number) => ReactNode` | ✅ | Function to render each card |
| `onSwipeLeft` | `(item: IProperty, index: number) => void` | ❌ | Callback when card is swiped left |
| `onSwipeRight` | `(item: IProperty, index: number) => void` | ❌ | Callback when card is swiped right |
| `onSwipeComplete` | `(direction: 'left' \| 'right', item: IProperty, index: number) => void` | ❌ | Callback when swipe animation completes |
| `OverlayLabelLeft` | `React.ComponentType` | ❌ | Component for left swipe overlay |
| `OverlayLabelRight` | `React.ComponentType` | ❌ | Component for right swipe overlay |
| `cardStyle` | `object` | ❌ | Additional styles for cards |

### PropertyCard Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `property` | `IProperty` | ✅ | Property object to display |
| `onPress` | `(propertyId: string) => void` | ❌ | Callback when card is pressed |
| `style` | `object` | ❌ | Additional styles for the card |

## Performance Optimizations

1. **Single Card Rendering**: Only renders current and next card for maximum performance
2. **State-based Updates**: Avoids reading shared values during render to prevent Reanimated warnings
3. **Zoom Animation**: Smooth next card zoom effect from 0.8x to 0.9x scale
4. **Memoization**: Property card components are memoized with custom comparison
5. **Image Optimization**: Uses Expo Image with caching and optimized loading
6. **Animation Cleanup**: Proper cleanup of animations on unmount
7. **Gesture Optimization**: Efficient gesture handling with minimal re-renders
8. **NativeWind Styling**: Uses Tailwind CSS classes for consistent styling and better performance
9. **Responsive Design**: Maximum card width of 425px for optimal viewing on all devices
10. **Container Padding**: Prevents card clipping with proper container spacing

## Animation Configuration

The component uses optimized spring configurations:

```tsx
const SPRING_CONFIG = {
  damping: 20,
  stiffness: 300,
  mass: 1,
  overshootClamping: false,
  restDisplacementThreshold: 0.01,
  restSpeedThreshold: 2,
};
```

## Constants

- `SWIPE_THRESHOLD`: 25% of screen width
- `ROTATION_ANGLE`: 15 degrees
- `SCALE_FACTOR`: 0.95 for animations
- `CARD_WIDTH`: 90% of screen width (max 425px)
- `CARD_HEIGHT`: 75% of screen height

## Dependencies

- `react-native-reanimated`: ^4.0.2
- `react-native-gesture-handler`: ^2.28.0
- `expo-image`: ^3.0.5

## Integration Example

The component is already integrated in the home screen (`apps/customer-mobile-mydeer/src/app/(app)/home/<USER>
