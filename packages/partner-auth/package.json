{"name": "@repo/partner-auth", "version": "0.1.0", "private": true, "type": "module", "main": "./src/index.ts", "types": "./src/index.ts", "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "dependencies": {"@repo/database": "*", "@repo/msg91": "*", "@auth/core": "0.34.2", "@auth/prisma-adapter": "^1.6.0", "@t3-oss/env-nextjs": "^0.10.1", "next": "15.5.2", "next-auth": "5.0.0-beta.25", "react": "19.1.0", "react-dom": "19.1.0", "zod": "^3.23.8"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/prettier-config": "*", "@repo/tsconfig": "*", "@types/react": "19.1.12", "eslint": "^9.12.0", "prettier": "^3.3.3", "typescript": "^5.8.2"}, "prettier": "@repo/prettier-config"}