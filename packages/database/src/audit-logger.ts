import type { PrismaClient } from "@prisma/client";
import type { AuditActionEnum, AuditEntityEnum } from "@prisma/client";

export interface AuditLogData {
  action: AuditActionEnum;
  entityType: AuditEntityEnum;
  entityId: string;
  entityTitle?: string;
  performedById: string;
  performedByName: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  reason?: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

export interface AuditContext {
  adminId: string;
  adminName: string;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Creates an audit log entry
 */
export async function createAuditLog(
  db: Omit<PrismaClient, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">,
  data: AuditLogData
): Promise<void> {
  try {
    const changes = data.oldValues && data.newValues 
      ? calculateChanges(data.oldValues, data.newValues)
      : {};

    await db.auditLog.create({
      data: {
        action: data.action,
        entityType: data.entityType,
        entityId: data.entityId,
        entityTitle: data.entityTitle,
        performedById: data.performedById,
        performedByName: data.performedByName,
        oldValues: data.oldValues,
        newValues: data.newValues,
        changes,
        reason: data.reason,
        metadata: data.metadata,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
      },
    });
  } catch (error) {
    // Log the error but don't throw to avoid breaking the main operation
    console.error("Failed to create audit log:", error);
  }
}

/**
 * Calculates the differences between old and new values
 */
export function calculateChanges(
  oldValues: Record<string, any>,
  newValues: Record<string, any>
): Record<string, { from: any; to: any }> {
  const changes: Record<string, { from: any; to: any }> = {};

  // Check for changed and new fields
  for (const [key, newValue] of Object.entries(newValues)) {
    const oldValue = oldValues[key];
    if (!isEqual(oldValue, newValue)) {
      changes[key] = { from: oldValue, to: newValue };
    }
  }

  // Check for removed fields
  for (const [key, oldValue] of Object.entries(oldValues)) {
    if (!(key in newValues)) {
      changes[key] = { from: oldValue, to: undefined };
    }
  }

  return changes;
}

/**
 * Deep equality check for values
 */
function isEqual(a: any, b: any): boolean {
  if (a === b) return true;
  if (a == null || b == null) return a === b;
  if (typeof a !== typeof b) return false;
  
  if (typeof a === "object") {
    if (Array.isArray(a) !== Array.isArray(b)) return false;
    
    if (Array.isArray(a)) {
      if (a.length !== b.length) return false;
      return a.every((item, index) => isEqual(item, b[index]));
    }
    
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    if (keysA.length !== keysB.length) return false;
    
    return keysA.every(key => isEqual(a[key], b[key]));
  }
  
  return false;
}

/**
 * Sanitizes sensitive data from audit logs
 */
export function sanitizeForAudit(data: Record<string, any>): Record<string, any> {
  const sensitiveFields = [
    'password',
    'passwordHash',
    'token',
    'refreshToken',
    'resetPasswordToken',
    'apiKey',
    'secret',
  ];

  const sanitized = { ...data };
  
  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  return sanitized;
}

/**
 * Helper function to log property actions
 */
export async function logPropertyAction(
  db: Omit<PrismaClient, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">,
  action: AuditActionEnum,
  propertyId: string,
  propertyTitle: string | null | undefined,
  context: AuditContext,
  options: {
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    reason?: string;
    metadata?: Record<string, any>;
  } = {}
): Promise<void> {
  await createAuditLog(db, {
    action,
    entityType: "PROPERTY",
    entityId: propertyId,
    entityTitle: propertyTitle || undefined,
    performedById: context.adminId,
    performedByName: context.adminName,
    oldValues: options.oldValues ? sanitizeForAudit(options.oldValues) : undefined,
    newValues: options.newValues ? sanitizeForAudit(options.newValues) : undefined,
    reason: options.reason,
    metadata: options.metadata,
    ipAddress: context.ipAddress,
    userAgent: context.userAgent,
  });
}

/**
 * Helper function to log partner/user actions
 */
export async function logPartnerAction(
  db: PrismaClient,
  action: AuditActionEnum,
  userId: string,
  userName: string | null | undefined,
  context: AuditContext,
  options: {
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    reason?: string;
    metadata?: Record<string, any>;
  } = {}
): Promise<void> {
  await createAuditLog(db, {
    action,
    entityType: "PARTNER",
    entityId: userId,
    entityTitle: userName || undefined,
    performedById: context.adminId,
    performedByName: context.adminName,
    oldValues: options.oldValues ? sanitizeForAudit(options.oldValues) : undefined,
    newValues: options.newValues ? sanitizeForAudit(options.newValues) : undefined,
    reason: options.reason,
    metadata: options.metadata,
    ipAddress: context.ipAddress,
    userAgent: context.userAgent,
  });
}

/**
 * Helper function to log post actions
 */
export async function logPostAction(
  db: PrismaClient,
  action: AuditActionEnum,
  postId: string,
  postContent: string | null | undefined,
  context: AuditContext,
  options: {
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    reason?: string;
    metadata?: Record<string, any>;
  } = {}
): Promise<void> {
  // Truncate content for title
  const entityTitle = postContent 
    ? postContent.length > 50 
      ? postContent.substring(0, 50) + "..."
      : postContent
    : undefined;

  await createAuditLog(db, {
    action,
    entityType: "POST",
    entityId: postId,
    entityTitle,
    performedById: context.adminId,
    performedByName: context.adminName,
    oldValues: options.oldValues ? sanitizeForAudit(options.oldValues) : undefined,
    newValues: options.newValues ? sanitizeForAudit(options.newValues) : undefined,
    reason: options.reason,
    metadata: options.metadata,
    ipAddress: context.ipAddress,
    userAgent: context.userAgent,
  });
}

/**
 * Extract audit context from tRPC context
 */
export function extractAuditContext(ctx: any): AuditContext {
  return {
    adminId: ctx.session?.user?.id || 'unknown',
    adminName: ctx.session?.user?.name || 'Unknown Admin',
    // Note: IP address and user agent would need to be passed from the request
    // This would require modifications to the tRPC context setup
  };
}
