{"name": "@repo/ui", "version": "0.1.0", "private": true, "scripts": {"ui:add": "pnpm dlx shadcn-ui@latest add", "lint": "eslint ."}, "peerDependencies": {"react": "19.1.0"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/tsconfig": "*", "@types/node": "20.19.12", "@types/react": "19.1.12", "autoprefixer": "^10.4.19", "postcss": "^8.4.47", "postcss-load-config": "^6.0.1", "react-google-autocomplete": "2.7.3", "tailwindcss": "^3.4.15", "typescript": "^5.8.2"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.8", "@repo/database": "*", "@tanstack/react-table": "^8.20.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "lucide-react": "^0.395.0", "next-cloudinary": "^6.16.0", "next-themes": "^0.3.0", "react-day-picker": "^8.10.1", "react-hook-form": "^7.53.0", "recharts": "^2.13.0", "sonner": "^1.5.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.23.8"}, "exports": {"./globals.css": "./src/globals.css", "./postcss.config": "./postcss.config.mjs", "./tailwind.config": "./tailwind.config.ts", "./lib/*": "./src/lib/*.ts", "./components/*": ["./src/components/*.tsx", "./src/components/*.ts"], "./hooks/*": ["./src/hooks/*.tsx", "./src/hooks/*.ts"]}}